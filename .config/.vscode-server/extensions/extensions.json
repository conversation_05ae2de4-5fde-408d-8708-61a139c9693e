[{"identifier": {"id": "github.vscode-pull-request-github", "uuid": "69ddd764-339a-4ecc-97c1-9c4ece58e36d"}, "version": "0.110.0", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0", "scheme": "file"}, "relativeLocation": "github.vscode-pull-request-github-0.110.0", "metadata": {"installedTimestamp": 1748881475756, "source": "gallery", "id": "69ddd764-339a-4ecc-97c1-9c4ece58e36d", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "publisherDisplayName": "GitHub", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "github.copilot-chat", "uuid": "7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f"}, "version": "0.27.2", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2", "scheme": "file"}, "relativeLocation": "github.copilot-chat-0.27.2", "metadata": {"installedTimestamp": 1748881478838, "source": "vsix", "id": "7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f", "publisherDisplayName": "GitHub", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "christian-kohler.npm-intellisense", "uuid": "************************************"}, "version": "1.4.5", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5", "scheme": "file"}, "relativeLocation": "christian-kohler.npm-intellisense-1.4.5", "metadata": {"installedTimestamp": 1748881519244, "pinned": false, "source": "gallery", "id": "************************************", "publisherId": "a892fb38-060b-475f-9e73-2e06a8a17a68", "publisherDisplayName": "<PERSON>", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "augment.vscode-augment", "uuid": "fc0e137d-e132-47ed-9455-c4636fa5b897"}, "version": "0.470.1", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.470.1", "scheme": "file"}, "relativeLocation": "augment.vscode-augment-0.470.1", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1748886334731, "pinned": false, "source": "gallery", "id": "fc0e137d-e132-47ed-9455-c4636fa5b897", "publisherId": "7814b14b-491a-4e83-83ac-9222fa835050", "publisherDisplayName": "Augment Computing", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "saoudrizwan.claude-dev", "uuid": "ad94c633-a9d5-4f78-b85f-c664e7d91a0f"}, "version": "3.17.9", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/saoudrizwan.claude-dev-3.17.9", "scheme": "file"}, "relativeLocation": "saoudrizwan.claude-dev-3.17.9", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1748920672621, "pinned": false, "source": "gallery", "id": "ad94c633-a9d5-4f78-b85f-c664e7d91a0f", "publisherId": "6f42e67d-d7ea-4a53-8704-2ae8bf2d86f3", "publisherDisplayName": "Cline", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "github.copilot"}, "version": "1.327.1600", "location": {"$mid": 1, "fsPath": "/home/<USER>/.vscode-server/extensions/github.copilot-1.327.1600", "external": "file:///home/<USER>/.vscode-server/extensions/github.copilot-1.327.1600", "path": "/home/<USER>/.vscode-server/extensions/github.copilot-1.327.1600", "scheme": "file"}, "relativeLocation": "github.copilot-1.327.1600", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1748920672922, "pinned": false, "source": "gallery", "id": "23c4aeee-f844-43cd-b53e-1113e483f1a6", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "publisherDisplayName": "GitHub", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": true, "hasPreReleaseVersion": true, "preRelease": true}}]