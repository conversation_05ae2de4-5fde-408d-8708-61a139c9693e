syntax = "proto3";

package cline;
option java_package = "bot.cline.proto";
option java_multiple_files = true;

import "common.proto";

// Enum for webview provider types
enum WebviewProviderType {
  SIDEBAR = 0;
  TAB = 1;
}

// Define a new message type for webview provider info
message WebviewProviderTypeRequest {
  Metadata metadata = 1;
  WebviewProviderType providerType = 2;
}

// UiService provides methods for managing UI interactions
service UiService {
  // Scrolls to a specific settings section in the settings view
  rpc scrollToSettings(StringRequest) returns (Empty);
  
  // Marks the current announcement as shown and returns whether an announcement should still be shown
  rpc onDidShowAnnouncement(EmptyRequest) returns (Boolean);
  
  // Subscribe to addToInput events (when user adds content via context menu)
  rpc subscribeToAddToInput(EmptyRequest) returns (stream String);
  
  // Subscribe to MCP button clicked events
  rpc subscribeToMcpButtonClicked(WebviewProviderTypeRequest) returns (stream Empty);
  
  // Subscribe to history button click events
  rpc subscribeToHistoryButtonClicked(WebviewProviderTypeRequest) returns (stream Empty);
}
