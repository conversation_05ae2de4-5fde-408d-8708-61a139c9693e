{
	"compilerOptions": {
		"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
		"target": "ES2020",
		"useDefineForClassFields": true,
		"lib": ["ES2020", "DOM", "DOM.Iterable"],
		"types": ["vitest/globals", "@testing-library/jest-dom"],
		"module": "ESNext",
		"skipLibCheck": true,

		/* Bundler mode */
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"isolatedModules": true,
		"moduleDetection": "force",
		"noEmit": true,
		"jsx": "react-jsx",

		/* Linting */
		"strict": true,
		"noFallthroughCasesInSwitch": true,
		"noUncheckedSideEffectImports": true,

		/* Aliasing */
		"baseUrl": ".",
		"paths": {
			"@/*": ["src/*"],
			"@components/*": ["src/components/*"],
			"@context/*": ["src/context/*"],
			"@shared/*": ["../src/shared/*"],
			"@utils/*": ["src/utils/*"]
		}
	},
	"include": ["src"]
}
