*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[05:39:54] 




[05:39:54] Extension host agent started.
[node.js fs] readdir with filetypes failed with error:  Error: ENOENT: no such file or directory, scandir '/home/<USER>/.vscode-server/extensions/github.copilot-1.326.1596'
    at async Object.readdir (node:internal/fs/promises:952:18)
    at async SC (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:43:4567)
    at async Object.Ah (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:43:4496)
    at async Ou.readdir (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:67:16002)
    at async Sl.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:64:51815)
    at async Sl.resolve (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:64:50931)
    at async yf.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:25227)
    at async Promise.all (index 2)
    at async yf.scanAllUserExtensions (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:20374)
    at async R8.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:9952)
    at async R8.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:7847) {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/home/<USER>/.vscode-server/extensions/github.copilot-1.326.1596'
}
[node.js fs] readdir with filetypes failed with error:  Error: ENOENT: no such file or directory, scandir '/home/<USER>/.vscode-server/extensions/saoudrizwan.claude-dev-3.17.8'
    at async Object.readdir (node:internal/fs/promises:952:18)
    at async SC (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:43:4567)
    at async Object.Ah (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:43:4496)
    at async Ou.readdir (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:67:16002)
    at async Sl.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:64:51815)
    at async Sl.resolve (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:64:50931)
    at async yf.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:25227)
    at async Promise.all (index 6)
    at async yf.scanAllUserExtensions (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:20374)
    at async R8.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:9952)
    at async R8.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:55:7847) {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/home/<USER>/.vscode-server/extensions/saoudrizwan.claude-dev-3.17.8'
}
[05:39:54] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.326.1596
[05:39:54] Deleted marked for removal extension from disk saoudrizwan.claude-dev /home/<USER>/.vscode-server/extensions/saoudrizwan.claude-dev-3.17.8
[05:39:54] [<unknown>][ee860244][ExtensionHostConnection] New connection established.
[05:39:54] [<unknown>][9ccbf11c][ManagementConnection] New connection established.
[05:39:54] [<unknown>][ee860244][ExtensionHostConnection] <21125> Launched Extension Host Process.
[05:39:55] ComputeTargetPlatform: linux-x64
[05:39:56] [<unknown>][df4eb9b5][ManagementConnection] New connection established.
[05:39:56] [<unknown>][5e95db75][ExtensionHostConnection] New connection established.
[05:39:56] [<unknown>][5e95db75][ExtensionHostConnection] <21221> Launched Extension Host Process.
[05:39:58] ComputeTargetPlatform: linux-x64
New EH opened, aborting shutdown
[05:44:54] New EH opened, aborting shutdown
[05:45:04] [<unknown>][9ccbf11c][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[05:45:04] [<unknown>][ee860244][ExtensionHostConnection] <21125> Extension Host Process exited with code: 0, signal: null.
