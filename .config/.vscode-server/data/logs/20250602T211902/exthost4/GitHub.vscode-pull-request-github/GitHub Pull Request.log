2025-06-03 03:17:54.296 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-03 03:17:54.296 [info] [Activation] Extension version: 0.110.0
2025-06-03 03:17:55.029 [info] [Authentication] Creating hub for .com
2025-06-03 03:17:56.163 [info] [Activation] Looking for git repository
2025-06-03 03:17:56.163 [info] [Activation] Found 0 repositories during activation
2025-06-03 03:17:56.163 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-03 03:17:56.168 [info] [GitAPI] Registering git provider
2025-06-03 03:17:56.168 [info] [Review+0] Validate state in progress
2025-06-03 03:17:56.168 [info] [Review+0] Validating state...
2025-06-03 03:17:56.726 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-03 03:17:57.076 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-03 03:17:57.317 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-03 03:17:57.508 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
2025-06-03 03:17:57.667 [info] [Review+0] No matching pull request metadata found on GitHub for current branch main
2025-06-03 03:23:29.092 [info] [Activation] Repo state for file:///home/<USER>/workspace changed.
2025-06-03 03:23:29.092 [info] [Activation] Repo file:///home/<USER>/workspace has already been setup.
