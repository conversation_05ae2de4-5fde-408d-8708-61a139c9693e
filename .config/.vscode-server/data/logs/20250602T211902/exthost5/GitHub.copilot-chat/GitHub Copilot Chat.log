2025-06-03 05:01:33.292 [info] Can't use the Electron fetcher in this environment.
2025-06-03 05:01:33.292 [info] Using the Node fetch fetcher.
2025-06-03 05:01:33.292 [info] Initializing Git extension service.
2025-06-03 05:01:33.292 [info] Successfully activated the vscode.git extension.
2025-06-03 05:01:33.292 [info] Enablement state of the vscode.git extension: true.
2025-06-03 05:01:33.292 [info] Successfully registered Git commit message provider.
2025-06-03 05:01:34.925 [info] Logged in as Chewy42
2025-06-03 05:01:36.061 [info] Got Copilot token for Chewy42
2025-06-03 05:01:36.979 [info] Fetched model metadata in 910ms b193052e-b718-46c3-9018-e3ca27dff4f5
2025-06-03 05:01:37.000 [info] activationBlocker from 'languageModelAccess' took for 3727ms
2025-06-03 05:01:37.259 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-03 05:01:37.275 [info] Registering default platform agent...
2025-06-03 05:01:37.463 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-03 05:01:37.463 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-03 05:01:37.463 [info] Successfully registered GitHub PR title and description provider.
2025-06-03 05:01:37.463 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-03 05:01:37.627 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-06-03 05:02:53.555 [info] TypeScript server plugin activated.
2025-06-03 05:02:53.555 [info] Registered TypeScript context provider with Copilot inline completions.
2025-06-03 05:31:40.264 [info] Logged in as Chewy42
2025-06-03 05:31:41.087 [info] Got Copilot token for Chewy42
2025-06-03 05:31:41.603 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-03 05:31:41.720 [info] BYOK: Copilot Chat known models list fetched successfully.
