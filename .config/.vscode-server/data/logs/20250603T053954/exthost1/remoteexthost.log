2025-06-03 05:39:56.019 [info] Extension host with pid 21125 started
2025-06-03 05:39:56.019 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/f2af1bbd16eb6c9d4c0cbe5065258ed9/vscode.lock'
2025-06-03 05:39:56.019 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/f2af1bbd16eb6c9d4c0cbe5065258ed9/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-03 05:39:56.025 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/f2af1bbd16eb6c9d4c0cbe5065258ed9/vscode.lock': The pid 15809 appears to be gone.
2025-06-03 05:39:56.025 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/f2af1bbd16eb6c9d4c0cbe5065258ed9/vscode.lock': Deleting a stale lock.
2025-06-03 05:39:56.045 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/f2af1bbd16eb6c9d4c0cbe5065258ed9/vscode.lock': Lock acquired.
2025-06-03 05:39:56.198 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onView:augment-chat'
2025-06-03 05:39:56.200 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-03 05:39:56.200 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-03 05:39:56.200 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-03 05:39:56.201 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-03 05:39:56.201 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-03 05:39:56.202 [info] ExtensionService#_doActivateExtension saoudrizwan.claude-dev, startup: false, activationEvent: 'onLanguage'
2025-06-03 05:39:58.279 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-03 05:39:58.486 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-03 05:39:58.487 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-03 05:39:59.076 [info] Eager extensions activated
2025-06-03 05:39:59.076 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-03 05:39:59.077 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-03 05:39:59.078 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-06-03 05:39:59.078 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-06-03 05:40:01.027 [info] ExtensionService#_doActivateExtension GitHub.vscode-pull-request-github, startup: false, activationEvent: 'onStartupFinished'
2025-06-03 05:40:04.923 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-03 05:40:09.439 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Cmfave%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at n_e.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1276:8652)
2025-06-03 05:45:04.099 [info] Extension host terminating: received terminate message from renderer
2025-06-03 05:45:04.100 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/f2af1bbd16eb6c9d4c0cbe5065258ed9/vscode.lock': Marking the lockfile as scheduled to be released in 6000 ms.
2025-06-03 05:45:04.161 [error] Error: Channel has been closed
    at s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/api/node/extensionHostProcess.js:125:2431)
    at Object.appendLine (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/api/node/extensionHostProcess.js:125:2570)
    at Function.log (/home/<USER>/workspace/.config/.vscode-server/extensions/saoudrizwan.claude-dev-3.17.9/dist/extension.js:960:35762)
    at Object.Vbc (/home/<USER>/workspace/.config/.vscode-server/extensions/saoudrizwan.claude-dev-3.17.9/dist/extension.js:4143:1690)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 15)
    at DJ.$ (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/api/node/extensionHostProcess.js:123:9888)
2025-06-03 05:45:04.165 [info] Extension host with pid 21125 exiting with code 0
