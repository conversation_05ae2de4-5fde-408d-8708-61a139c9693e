2025-06-03 05:40:02.689 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-03 05:40:02.689 [info] [Activation] Extension version: 0.110.0
2025-06-03 05:40:02.708 [info] [Authentication] Creating hub for .com
2025-06-03 05:40:03.433 [info] [Activation] Looking for git repository
2025-06-03 05:40:03.434 [info] [Activation] Found 0 repositories during activation
2025-06-03 05:40:03.434 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-03 05:40:03.506 [info] [GitAPI] Registering git provider
2025-06-03 05:40:04.562 [info] [Activation] Git initialization state changed: state=initialized
2025-06-03 05:40:04.562 [info] [Review+0] Validate state in progress
2025-06-03 05:40:04.562 [info] [Review+0] Validating state...
2025-06-03 05:40:04.693 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-03 05:40:04.888 [info] [Activation] Repo state for file:///home/<USER>/workspace changed.
2025-06-03 05:40:04.888 [info] [Activation] Repo file:///home/<USER>/workspace has already been setup.
2025-06-03 05:40:05.265 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-03 05:40:05.574 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-03 05:40:08.329 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
2025-06-03 05:40:08.364 [info] [Review+0] No matching pull request metadata found on GitHub for current branch main
