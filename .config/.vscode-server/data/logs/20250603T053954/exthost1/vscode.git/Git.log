2025-06-03 05:40:01.151 [info] [main] Log level: Info
2025-06-03 05:40:01.151 [info] [main] Validating found git in: "git"
2025-06-03 05:40:01.151 [info] [main] Using git "2.47.2" from "git"
2025-06-03 05:40:01.151 [info] [Model][doInitialScan] Initial repository scan started
2025-06-03 05:40:01.152 [info] > git rev-parse --show-toplevel [1297ms]
2025-06-03 05:40:01.152 [info] > git rev-parse --git-dir --git-common-dir [499ms]
2025-06-03 05:40:01.152 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-03 05:40:01.152 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-03 05:40:01.152 [info] > git config --get commit.template [223ms]
2025-06-03 05:40:01.508 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [207ms]
2025-06-03 05:40:02.603 [info] > git status -z -uall [31ms]
2025-06-03 05:40:02.666 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [87ms]
2025-06-03 05:40:02.687 [info] > git rev-parse --show-toplevel [427ms]
2025-06-03 05:40:02.762 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [78ms]
2025-06-03 05:40:02.823 [info] > git config --get --local branch.main.vscode-merge-base [53ms]
2025-06-03 05:40:02.830 [info] > git rev-parse --show-toplevel [131ms]
2025-06-03 05:40:02.831 [info] > git config --get commit.template [136ms]
2025-06-03 05:40:02.847 [info] > git rev-parse --show-toplevel [9ms]
2025-06-03 05:40:02.847 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [17ms]
2025-06-03 05:40:02.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [19ms]
2025-06-03 05:40:02.865 [info] > git rev-parse --show-toplevel [6ms]
2025-06-03 05:40:02.865 [info] > git merge-base refs/heads/main refs/remotes/origin/main [11ms]
2025-06-03 05:40:02.885 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [15ms]
2025-06-03 05:40:03.082 [info] > git rev-parse --show-toplevel [208ms]
2025-06-03 05:40:03.106 [info] > git status -z -uall [214ms]
2025-06-03 05:40:03.106 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [209ms]
2025-06-03 05:40:03.167 [info] > git rev-parse --show-toplevel [45ms]
2025-06-03 05:40:03.287 [info] > git rev-parse --show-toplevel [60ms]
2025-06-03 05:40:03.796 [info] > git rev-parse --show-toplevel [501ms]
2025-06-03 05:40:04.044 [info] > git rev-parse --show-toplevel [239ms]
2025-06-03 05:40:04.379 [info] > git rev-parse --show-toplevel [104ms]
2025-06-03 05:40:04.402 [info] > git rev-parse --show-toplevel [4ms]
2025-06-03 05:40:04.449 [info] > git rev-parse --show-toplevel [24ms]
2025-06-03 05:40:04.548 [info] > git rev-parse --show-toplevel [81ms]
2025-06-03 05:40:04.558 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 05:40:04.560 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-03 05:40:04.777 [info] > git config --get commit.template [129ms]
2025-06-03 05:40:04.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [62ms]
2025-06-03 05:40:04.884 [info] > git status -z -uall [17ms]
2025-06-03 05:40:04.885 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-03 05:40:05.573 [info] > git config --get --local branch.main.github-pr-owner-number [297ms]
2025-06-03 05:40:05.574 [warning] [Git][config] git config failed: Failed to execute git
2025-06-03 05:40:08.657 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [18ms]
2025-06-03 05:40:08.657 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-03 05:40:08.827 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [158ms]
2025-06-03 05:42:08.641 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [1ms]
2025-06-03 05:42:08.641 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-03 05:42:08.656 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [4ms]
2025-06-03 05:44:08.636 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-03 05:44:08.636 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-03 05:44:08.644 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-03 05:44:59.138 [info] > git fetch [185ms]
2025-06-03 05:44:59.139 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-03 05:44:59.158 [info] > git config --get commit.template [10ms]
2025-06-03 05:44:59.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 05:44:59.172 [info] > git status -z -uall [6ms]
2025-06-03 05:44:59.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
