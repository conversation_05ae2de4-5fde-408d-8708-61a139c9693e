2025-06-03 05:40:02.608 [info] Can't use the Electron fetcher in this environment.
2025-06-03 05:40:02.608 [info] Using the Node fetch fetcher.
2025-06-03 05:40:02.608 [info] Initializing Git extension service.
2025-06-03 05:40:02.609 [info] Successfully activated the vscode.git extension.
2025-06-03 05:40:02.609 [info] Enablement state of the vscode.git extension: true.
2025-06-03 05:40:02.609 [info] Successfully registered Git commit message provider.
2025-06-03 05:40:03.436 [info] Logged in as Chewy42
2025-06-03 05:40:08.316 [info] Got Copilot token for Chewy42
2025-06-03 05:40:08.895 [info] Fetched model metadata in 574ms 4358efaa-5b93-4948-9219-ba9763480e39
2025-06-03 05:40:08.909 [info] activationBlocker from 'languageModelAccess' took for 6735ms
2025-06-03 05:40:09.122 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-03 05:40:09.145 [info] Registering default platform agent...
2025-06-03 05:40:09.405 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-03 05:40:09.405 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-03 05:40:09.405 [info] Successfully registered GitHub PR title and description provider.
2025-06-03 05:40:09.405 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-03 05:40:09.489 [info] BYOK: Copilot Chat known models list fetched successfully.
