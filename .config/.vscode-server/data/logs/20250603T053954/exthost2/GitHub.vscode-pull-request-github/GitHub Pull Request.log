2025-06-03 05:40:05.276 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-03 05:40:05.276 [info] [Activation] Extension version: 0.110.0
2025-06-03 05:40:05.916 [info] [Authentication] Creating hub for .com
2025-06-03 05:40:06.937 [info] [Activation] Looking for git repository
2025-06-03 05:40:06.937 [info] [Activation] Found 0 repositories during activation
2025-06-03 05:40:06.938 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-03 05:40:06.947 [info] [GitAPI] Registering git provider
2025-06-03 05:40:06.951 [info] [Review+0] Validate state in progress
2025-06-03 05:40:06.953 [info] [Review+0] Validating state...
2025-06-03 05:40:07.261 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-03 05:40:08.027 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-03 05:40:08.226 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-03 05:40:08.671 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
2025-06-03 05:40:08.997 [info] [Review+0] No matching pull request metadata found on GitHub for current branch main
2025-06-03 05:40:11.906 [info] [Activation] Repo state for file:///home/<USER>/workspace changed.
2025-06-03 05:40:11.906 [info] [Activation] Repo file:///home/<USER>/workspace has already been setup.
