2025-06-03 05:40:03.941 [info] [main] Log level: Info
2025-06-03 05:40:03.941 [info] [main] Validating found git in: "git"
2025-06-03 05:40:03.941 [info] [main] Using git "2.47.2" from "git"
2025-06-03 05:40:03.941 [info] [Model][doInitialScan] Initial repository scan started
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [5ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-03 05:40:03.941 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-03 05:40:03.941 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-03 05:40:03.941 [info] > git config --get commit.template [2ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [467ms]
2025-06-03 05:40:03.941 [info] > git status -z -uall [55ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [12ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [55ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [40ms]
2025-06-03 05:40:03.941 [info] > git config --get --local branch.main.vscode-merge-base [24ms]
2025-06-03 05:40:03.941 [info] > git config --get commit.template [57ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [66ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [11ms]
2025-06-03 05:40:03.941 [info] > git merge-base refs/heads/main refs/remotes/origin/main [3ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [19ms]
2025-06-03 05:40:03.941 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [6ms]
2025-06-03 05:40:03.941 [info] > git status -z -uall [8ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [18ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [9ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [1ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [11ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [8ms]
2025-06-03 05:40:03.942 [info] > git rev-parse --show-toplevel [46ms]
2025-06-03 05:40:03.942 [info] > git rev-parse --show-toplevel [5ms]
2025-06-03 05:40:03.942 [info] > git rev-parse --show-toplevel [1ms]
2025-06-03 05:40:03.942 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 05:40:03.942 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-03 05:40:04.538 [info] > git config --get commit.template [24ms]
2025-06-03 05:40:04.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-03 05:40:05.327 [info] > git status -z -uall [22ms]
2025-06-03 05:40:05.334 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [21ms]
2025-06-03 05:40:08.225 [info] > git config --get --local branch.main.github-pr-owner-number [182ms]
2025-06-03 05:40:08.225 [warning] [Git][config] git config failed: Failed to execute git
2025-06-03 05:40:11.860 [info] > git config --get commit.template [42ms]
2025-06-03 05:40:11.861 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-03 05:40:11.901 [info] > git status -z -uall [10ms]
2025-06-03 05:40:11.902 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 05:40:12.089 [info] > git fetch [289ms]
2025-06-03 05:40:12.089 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-03 05:40:12.100 [info] > git config --get commit.template [2ms]
2025-06-03 05:40:12.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 05:40:12.142 [info] > git status -z -uall [10ms]
2025-06-03 05:40:12.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:41:17.837 [info] > git show --textconv :.replit [11ms]
2025-06-03 05:41:17.839 [info] > git ls-files --stage -- .replit [4ms]
2025-06-03 05:41:17.852 [info] > git cat-file -s 94429ce8e752f0414fc449966fbe9040ae43a887 [1ms]
2025-06-03 05:41:17.987 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-03 05:41:21.296 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- .replit [3ms]
2025-06-03 05:45:07.095 [info] > git ls-files --stage -- .replit [2ms]
2025-06-03 05:45:07.105 [info] > git cat-file -s 94429ce8e752f0414fc449966fbe9040ae43a887 [1ms]
2025-06-03 05:45:07.183 [info] > git show --textconv :.replit [1ms]
2025-06-03 05:45:07.538 [info] > git config --get commit.template [7ms]
2025-06-03 05:45:07.538 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:45:07.553 [info] > git status -z -uall [7ms]
2025-06-03 05:45:07.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:45:14.528 [info] > git config --get commit.template [2ms]
2025-06-03 05:45:14.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 05:45:14.560 [info] > git status -z -uall [6ms]
2025-06-03 05:45:14.561 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:45:19.582 [info] > git config --get commit.template [8ms]
2025-06-03 05:45:19.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 05:45:19.594 [info] > git status -z -uall [5ms]
2025-06-03 05:45:19.595 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 05:45:24.617 [info] > git config --get commit.template [6ms]
2025-06-03 05:45:24.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:45:24.632 [info] > git status -z -uall [7ms]
2025-06-03 05:45:24.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 05:45:35.330 [info] > git config --get commit.template [6ms]
2025-06-03 05:45:35.331 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 05:45:35.343 [info] > git status -z -uall [6ms]
2025-06-03 05:45:35.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
