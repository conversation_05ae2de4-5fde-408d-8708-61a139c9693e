2025-06-03 05:40:05.076 [info] Can't use the Electron fetcher in this environment.
2025-06-03 05:40:05.076 [info] Using the Node fetch fetcher.
2025-06-03 05:40:05.076 [info] Initializing Git extension service.
2025-06-03 05:40:05.076 [info] Successfully activated the vscode.git extension.
2025-06-03 05:40:05.076 [info] Enablement state of the vscode.git extension: true.
2025-06-03 05:40:05.076 [info] Successfully registered Git commit message provider.
2025-06-03 05:40:06.757 [info] Logged in as Chewy42
2025-06-03 05:40:08.777 [info] Got Copilot token for Chewy42
2025-06-03 05:40:09.241 [info] Fetched model metadata in 456ms 15dbd543-f392-49d1-a1de-99422d85f898
2025-06-03 05:40:11.135 [info] activationBlocker from 'languageModelAccess' took for 6767ms
2025-06-03 05:40:11.400 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-03 05:40:11.416 [info] Registering default platform agent...
2025-06-03 05:40:11.632 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-03 05:40:11.632 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-03 05:40:11.632 [info] Successfully registered GitHub PR title and description provider.
2025-06-03 05:40:11.632 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-03 05:40:11.766 [info] BYOK: Copilot Chat known models list fetched successfully.
