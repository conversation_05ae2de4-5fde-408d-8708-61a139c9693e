import { Hono } from "hono";
import type { Context } from "hono";
import type { AppVariables } from "../middleware/authMiddleware";

const flashcardRoutes = new Hono<{ Variables: AppVariables }>();

// Route to update an individual flashcard
flashcardRoutes.put("/:flashcardId", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");
  const flashcardId = c.req.param("flashcardId");

  if (!flashcardId) {
    return c.json({ error: "Invalid flashcard ID" }, 400);
  }

  try {
    const body = await c.req.json();
    const { front_text, back_text } = body;

    if (!front_text || !back_text) {
      return c.json({ error: "Both front_text and back_text are required" }, 400);
    }

    // First verify the flashcard exists and user owns it
    const { data: flashcard, error: flashcardError } = await supabase
      .from("flashcards")
      .select("id, user_id, set_id")
      .eq("id", flashcardId)
      .eq("user_id", user.id)
      .single();

    if (flashcardError) {
      console.error("Error checking flashcard ownership:", flashcardError);
      if (flashcardError.code === "PGRST116") {
        return c.json({ error: "Flashcard not found or you don't have access" }, 404);
      }
      return c.json(
        { error: "Failed to verify flashcard", details: flashcardError.message },
        500
      );
    }

    if (!flashcard) {
      return c.json({ error: "Flashcard not found or you don't have access" }, 404);
    }

    // Update the flashcard
    const { data: updatedFlashcard, error: updateError } = await supabase
      .from("flashcards")
      .update({
        front_text: front_text.trim(),
        back_text: back_text.trim(),
        updated_at: new Date().toISOString(),
      })
      .eq("id", flashcardId)
      .eq("user_id", user.id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating flashcard:", updateError);
      return c.json(
        { error: "Failed to update flashcard", details: updateError.message },
        500
      );
    }

    return c.json({
      message: "Flashcard updated successfully",
      flashcard: updatedFlashcard,
    }, 200);
  } catch (error: any) {
    console.error("Error in flashcard update:", error);
    return c.json({ error: error.message || "Failed to update flashcard" }, 500);
  }
});

// Route to delete an individual flashcard
flashcardRoutes.delete("/:flashcardId", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");
  const flashcardId = c.req.param("flashcardId");

  if (!flashcardId) {
    return c.json({ error: "Invalid flashcard ID" }, 400);
  }

  try {
    // First verify the flashcard exists and user owns it
    const { data: flashcard, error: flashcardError } = await supabase
      .from("flashcards")
      .select("id, user_id, set_id")
      .eq("id", flashcardId)
      .eq("user_id", user.id)
      .single();

    if (flashcardError) {
      console.error("Error checking flashcard ownership:", flashcardError);
      if (flashcardError.code === "PGRST116") {
        return c.json({ error: "Flashcard not found or you don't have access" }, 404);
      }
      return c.json(
        { error: "Failed to verify flashcard", details: flashcardError.message },
        500
      );
    }

    if (!flashcard) {
      return c.json({ error: "Flashcard not found or you don't have access" }, 404);
    }

    // Delete the flashcard
    const { error: deleteError } = await supabase
      .from("flashcards")
      .delete()
      .eq("id", flashcardId)
      .eq("user_id", user.id);

    if (deleteError) {
      console.error("Error deleting flashcard:", deleteError);
      return c.json(
        { error: "Failed to delete flashcard", details: deleteError.message },
        500
      );
    }

    return c.json({
      message: "Flashcard deleted successfully",
    }, 200);
  } catch (error: any) {
    console.error("Error in flashcard deletion:", error);
    return c.json({ error: error.message || "Failed to delete flashcard" }, 500);
  }
});

// Route to get an individual flashcard
flashcardRoutes.get("/:flashcardId", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");
  const flashcardId = c.req.param("flashcardId");

  if (!flashcardId) {
    return c.json({ error: "Invalid flashcard ID" }, 400);
  }

  try {
    // Get the flashcard if user owns it
    const { data: flashcard, error: flashcardError } = await supabase
      .from("flashcards")
      .select("*")
      .eq("id", flashcardId)
      .eq("user_id", user.id)
      .single();

    if (flashcardError) {
      console.error("Error fetching flashcard:", flashcardError);
      if (flashcardError.code === "PGRST116") {
        return c.json({ error: "Flashcard not found or you don't have access" }, 404);
      }
      return c.json(
        { error: "Failed to fetch flashcard", details: flashcardError.message },
        500
      );
    }

    if (!flashcard) {
      return c.json({ error: "Flashcard not found or you don't have access" }, 404);
    }

    return c.json(flashcard, 200);
  } catch (error: any) {
    console.error("Error in flashcard fetch:", error);
    return c.json({ error: error.message || "Failed to fetch flashcard" }, 500);
  }
});

export default flashcardRoutes;
