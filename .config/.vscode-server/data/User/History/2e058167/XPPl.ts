import { Hono } from "hono";
import type { Context } from "hono";
import type { AppVariables } from "../middleware/authMiddleware";

const flashcardRoutes = new Hono<{ Variables: AppVariables }>();

// POST /api/decks/:deckId/flashcards - Create a new flashcard in a deck
router.post("/decks/:deckId/flashcards", async (req: Request, res: Response) => {
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res
      .status(authResult.status)
      .json({ error: authResult.error, details: authResult.details });
  }
  const user = authResult.user;
  const { deckId } = req.params;
  try {
    // Verify deck ownership
    const deck = await db
      .select()
      .from(flashcardDecks)
      .where(
        and(
          eq(flashcardDecks.id, Number(deckId)),
          eq(flashcardDecks.userId, user.id)
        )
      );
    if (!deck || deck.length === 0) {
      return res.status(404).json({ error: "Deck not found or not owned by user" });
    }
    const body = req.body;
    const parsedBody = insertFlashcardSchema
      .omit({ deckId: true })
      .safeParse(body);
    if (!parsedBody.success) {
      return res
        .status(400)
        .json({ error: "Invalid flashcard data", details: parsedBody.error.flatten() });
    }
    const cardData = {
      ...parsedBody.data,
      deckId: Number(deckId),
      createdAt: Date.now(),
    };
    const newCard = await db.insert(flashcards).values(cardData).returning();
    if (!newCard || newCard.length === 0) {
      return res.status(500).json({ error: "Failed to create flashcard" });
    }
    return res.status(201).json(newCard[0]);
  } catch (error: any) {
    console.error("Error creating flashcard:", error);
    return res
      .status(500)
      .json({ error: "Failed to create flashcard", details: error.message });
  }
});

// GET /api/decks/:deckId/flashcards - Get all flashcards for a deck
router.get("/decks/:deckId/flashcards", async (req: Request, res: Response) => {
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res
      .status(authResult.status)
      .json({ error: authResult.error, details: authResult.details });
  }
  const user = authResult.user;
  const { deckId } = req.params;
  try {
    // Verify deck ownership
    const deck = await db
      .select()
      .from(flashcardDecks)
      .where(
        and(
          eq(flashcardDecks.id, Number(deckId)),
          eq(flashcardDecks.userId, user.id)
        )
      );
    if (!deck || deck.length === 0) {
      return res.status(404).json({ error: "Deck not found or not owned by user" });
    }
    const cards = await db
      .select()
      .from(flashcards)
      .where(eq(flashcards.deckId, Number(deckId)));
    return res.status(200).json(cards);
  } catch (error: any) {
    console.error("Error fetching flashcards:", error);
    return res
      .status(500)
      .json({ error: "Failed to fetch flashcards", details: error.message });
  }
});

export default router;
