import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Plus } from "lucide-react";
// Removed Supabase import - using backend API endpoints
import { useAuth } from "@/hooks/useAuth";
import { Tables } from "@/types/supabase";
import FlashcardForm from "./FlashcardForm";
import FlashcardsList from "./FlashcardsList";
import AiFlashcardGenerator from "./AiFlashcardGenerator";

type Flashcard = Tables<"flashcards">;

interface FlashcardEditManagerProps {
  selectedDeckId: string;
  selectedDeckName: string;
  onClose: () => void;
}

const FlashcardEditManager: React.FC<FlashcardEditManagerProps> = ({
  selectedDeckId,
  selectedDeckName,
  onClose,
}) => {
  const { user } = useAuth();
  const [editingFlashcard, setEditingFlashcard] = useState<Flashcard | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFlashcards = async () => {
    if (!user || !selectedDeckId) return;

    console.log("🔄 fetchFlashcards called for deck:", selectedDeckId);
    setIsLoading(true);
    setError(null);

    try {
      // Fetch flashcard set from backend API
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/flashcard-sets/${selectedDeckId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch flashcard set');
      }

      const data = await response.json();
      console.log("📋 Fetched flashcards data:", {
        flashcardCount: data.flashcards?.length || 0,
        flashcards: data.flashcards?.map((f: any) => ({
          id: f.id,
          front_text: f.front_text?.substring(0, 50) + "...",
          back_text: f.back_text?.substring(0, 50) + "...",
          updated_at: f.updated_at
        }))
      });
      setFlashcards(data.flashcards || []);
    } catch (err: any) {
      console.error("❌ Error fetching flashcards:", err);
      setError("Failed to fetch flashcards");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFlashcards();
    // Real-time subscriptions removed - using backend API only
  }, [user, selectedDeckId]);

  const handleFlashcardSaved = () => {
    setEditingFlashcard(null);
    setShowForm(false);
    fetchFlashcards();
  };

  const handleCancelEdit = () => {
    setEditingFlashcard(null);
    setShowForm(false);
  };

  const handleEditFlashcard = (flashcard: Flashcard) => {
    setEditingFlashcard(flashcard);
    setShowForm(true);
  };

  const handleAddNew = () => {
    setEditingFlashcard(null);
    setShowForm(true);
  };

  const handleGenerationComplete = () => {
    fetchFlashcards();
  };

  const handleUpdateFlashcard = (updatedFlashcard: Flashcard) => {
    console.log("🔄 Updating flashcard in local state:", updatedFlashcard.id);
    setFlashcards(prevFlashcards =>
      prevFlashcards.map(flashcard =>
        flashcard.id === updatedFlashcard.id ? updatedFlashcard : flashcard
      )
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={onClose}
            className="text-slate-400 hover:text-slate-300"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-slate-100">
              Edit Flashcards
            </h1>
            <p className="text-slate-400">{selectedDeckName}</p>
          </div>
        </div>
        <Button
          onClick={handleAddNew}
          className="bg-purple-600 hover:bg-purple-700 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Flashcard
        </Button>
      </div>

      {/* Manual Form */}
      {showForm && (
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-slate-100">
              {editingFlashcard ? "Edit Flashcard" : "Add New Flashcard"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FlashcardForm
              selectedDeckId={selectedDeckId}
              editingFlashcard={editingFlashcard}
              onFlashcardSaved={handleFlashcardSaved}
              onCancel={handleCancelEdit}
            />
          </CardContent>
        </Card>
      )}

      {/* AI Generator */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-slate-100">AI Flashcard Generator</CardTitle>
        </CardHeader>
        <CardContent>
          <AiFlashcardGenerator
            selectedDeckId={selectedDeckId}
            selectedDeckName={selectedDeckName}
            onGenerationComplete={handleGenerationComplete}
          />
        </CardContent>
      </Card>

      {/* Flashcards List */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-slate-100">
            Existing Flashcards ({flashcards.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <FlashcardsList
            flashcards={flashcards}
            loading={isLoading}
            onRefreshFlashcards={fetchFlashcards}
            onEditFlashcard={handleEditFlashcard}
            onUpdateFlashcard={handleUpdateFlashcard}
            selectedDeckId={selectedDeckId}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default FlashcardEditManager;
