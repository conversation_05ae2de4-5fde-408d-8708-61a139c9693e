# =============================================================================
# ChewyAI Environment Configuration
# =============================================================================

# =============================================================================
# CLIENT ENVIRONMENT VARIABLES (VITE_ prefix for Vite)
# =============================================================================
# These are exposed to the browser - only include non-sensitive values

# Supabase Configuration (Client)
VITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0qS0Nh1aV4GjZYniANoUZKDDcuCcQxzTm-DnTeZsNlQ

# API Configuration
# In development: use localhost:5000
# In production: use relative path (same origin)
VITE_API_BASE_URL=http://localhost:5000/api

# =============================================================================
# SERVER ENVIRONMENT VARIABLES
# =============================================================================
# These are only available on the server - include sensitive values here

# Application Configuration
NODE_ENV=development
PORT=5000

# Supabase Configuration (Server)
SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8

# Database Configuration
VITE_DATABASE_PASSWORD=your-database-password-here

# =============================================================================
# AI PROVIDER CONFIGURATION (Optional)
# =============================================================================
DEFAULT_AI_PROVIDER=openrouter
DEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash-preview-05-20
DEFAULT_GENERATION_MODEL=google/gemini-2.5-pro-preview

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=your-jwt-secret-change-this-in-production-make-it-very-long-and-random
SESSION_SECRET=your-session-secret-change-this-in-production-make-it-very-long-and-random

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
FRONTEND_URL=http://localhost:3000

# =============================================================================
# ADDITIONAL DATABASE CONFIGURATION (Optional)
# =============================================================================
DATABASE_URL=./data/chewyai.sqlite
