import React, { useEffect, useState, useRef, useCallback } from "react";
import { useAuth } from "../../hooks/useAuth";
import { Tables } from "../../types/supabase";
import { QuestionForm } from "./QuestionForm";
import { AiQuestionGenerator } from "./AiQuestionGenerator";
import { QuestionsList, QuestionsListProps } from "./QuestionsList";
import { Button } from "@/components/ui/button";

type QuizQuestion = Tables<"quiz_questions">;

interface QuizQuestionManagerProps {
  selectedQuizId: string;
  selectedQuizName: string;
  studyDocumentId?: string;
  onClose: () => void;
}

export const QuizQuestionManager: React.FC<QuizQuestionManagerProps> = ({
  selectedQuizId,
  selectedQuizName,
  studyDocumentId,
  onClose,
}) => {
  const { user, signOut } = useAuth();
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(
    null
  );

  // Refs to manage polling and prevent memory leaks
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);
  const retryCountRef = useRef(0);
  const abortControllerRef = useRef<AbortController | null>(null);
  const maxRetries = 3;
  const pollingInterval = 15000; // Increased to 15 seconds to reduce load

  // Clear polling interval and abort any ongoing requests
  const clearPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // Handle authentication errors - simplified to avoid dependency cycles
  const handleAuthError = useCallback(async () => {
    console.warn('Authentication failed in QuizQuestionManager, signing out...');
    // Clear polling first
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    try {
      await signOut();
    } catch (error) {
      console.error('Error during signOut:', error);
    }
  }, [signOut]); // Removed clearPolling dependency to break cycle

  const fetchQuestions = useCallback(async (isRetry = false) => {
    if (!user || !selectedQuizId || !mountedRef.current) return;

    // Cancel any previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    // Don't show loading on retries to avoid UI flicker
    if (!isRetry) {
      setLoading(true);
    }

    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`/api/quizzes/${selectedQuizId}/questions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        signal, // Add abort signal to request
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Authentication failed - handle it properly
          await handleAuthError();
          return;
        }

        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch questions`);
      }

      const data = await response.json();

      if (mountedRef.current && !signal.aborted) {
        setQuestions(data || []);
        setError(null);
        retryCountRef.current = 0; // Reset retry count on success
      }
    } catch (err: any) {
      // Don't log errors for aborted requests
      if (err.name === 'AbortError') {
        console.log('Request was cancelled');
        return;
      }

      console.error('Error fetching questions:', err);

      if (mountedRef.current && !signal.aborted) {
        // Only set error if we're still mounted and it's not an auth error or network error
        if (!err.message?.includes('authentication') &&
            !err.message?.includes('token') &&
            !err.message?.includes('Failed to fetch')) {
          setError(err.message || "Failed to fetch questions.");

          // Implement exponential backoff for retries (only for non-auth errors)
          if (retryCountRef.current < maxRetries) {
            retryCountRef.current++;
            const delay = Math.pow(2, retryCountRef.current) * 2000; // 4s, 8s, 16s
            console.log(`Retrying in ${delay}ms (attempt ${retryCountRef.current}/${maxRetries})`);

            setTimeout(() => {
              if (mountedRef.current) {
                fetchQuestions(true);
              }
            }, delay);
          }
        } else if (err.message?.includes('Failed to fetch')) {
          // Network error - stop polling to prevent spam
          console.warn('Network error detected, stopping polling to prevent request spam');
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          setError("Network connection lost. Please refresh the page to retry.");
        }
      }
    } finally {
      if (mountedRef.current && !isRetry && !signal.aborted) {
        setLoading(false);
      }
    }
  }, [user, selectedQuizId, handleAuthError]);

  // Simplified useEffect to break dependency cycles
  useEffect(() => {
    // Mark as mounted
    mountedRef.current = true;

    // Initial fetch
    if (user && selectedQuizId) {
      fetchQuestions();

      // Start polling after a delay to allow initial fetch to complete
      const startPollingTimer = setTimeout(() => {
        if (mountedRef.current && user && selectedQuizId) {
          intervalRef.current = setInterval(() => {
            if (mountedRef.current && user && selectedQuizId) {
              fetchQuestions(true); // Mark as retry to avoid loading state
            }
          }, pollingInterval);
        }
      }, 2000); // 2 second delay before starting polling

      return () => {
        clearTimeout(startPollingTimer);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
          abortControllerRef.current = null;
        }
      };
    }
  }, [user, selectedQuizId]); // Simplified dependencies - only the essential ones

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []); // Empty dependency array for cleanup only

  const handleQuestionSaved = useCallback(() => {
    setEditingQuestion(null);
    fetchQuestions();
  }, [fetchQuestions]);

  const handleCancelEdit = useCallback(() => {
    setEditingQuestion(null);
  }, []);

  const handleGenerationComplete = useCallback(() => {
    fetchQuestions();
  }, [fetchQuestions]);

  if (loading)
    return (
      <p className="text-slate-300">
        Loading questions for "{selectedQuizName}"...
      </p>
    );
  if (error) return <p className="text-red-400">Error: {error}</p>;

  return (
    <div className="mt-5 space-y-6">
      <div className="bg-slate-800 shadow-lg rounded-lg border border-slate-700 p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h4 className="text-2xl font-bold text-slate-200 mb-2">
              Manage Questions
            </h4>
            <p className="text-slate-400 text-sm">
              Quiz:{" "}
              <span className="text-purple-400 font-medium">
                {selectedQuizName}
              </span>
            </p>
          </div>
          <Button
            onClick={onClose}
            variant="outline"
            className="border-slate-600 text-slate-300 hover:text-white"
          >
            ← Back to Quizzes
          </Button>
        </div>

        {editingQuestion ? (
          <QuestionForm
            selectedQuizId={selectedQuizId}
            editingQuestion={editingQuestion}
            onQuestionSaved={handleQuestionSaved}
            onCancel={handleCancelEdit}
          />
        ) : (
          <QuestionForm
            selectedQuizId={selectedQuizId}
            onQuestionSaved={handleQuestionSaved}
            onCancel={handleCancelEdit}
          />
        )}

        <AiQuestionGenerator
          selectedQuizId={selectedQuizId}
          selectedQuizName={selectedQuizName}
          studyDocumentId={studyDocumentId}
          onGenerationComplete={handleGenerationComplete}
        />

        <QuestionsList
          questions={questions}
          loading={loading}
          onRefreshQuestions={fetchQuestions}
          selectedQuizId={selectedQuizId}
        />
      </div>
    </div>
  );
};
