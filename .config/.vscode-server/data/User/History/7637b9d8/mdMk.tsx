import React, { useEffect, useState, useRef, useCallback } from "react";
import { useAuth } from "../../hooks/useAuth";
import { Tables } from "../../types/supabase";
import { QuestionForm } from "./QuestionForm";
import { AiQuestionGenerator } from "./AiQuestionGenerator";
import { QuestionsList, QuestionsListProps } from "./QuestionsList";
import { Button } from "@/components/ui/button";

type QuizQuestion = Tables<"quiz_questions">;

interface QuizQuestionManagerProps {
  selectedQuizId: string;
  selectedQuizName: string;
  studyDocumentId?: string;
  onClose: () => void;
}

export const QuizQuestionManager: React.FC<QuizQuestionManagerProps> = ({
  selectedQuizId,
  selectedQuizName,
  studyDocumentId,
  onClose,
}) => {
  const { user, signOut } = useAuth();
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(
    null
  );

  // Refs to manage polling and prevent memory leaks
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);
  const retryCountRef = useRef(0);
  const maxRetries = 3;

  const fetchQuestions = async () => {
    if (!user || !selectedQuizId) return;
    setLoading(true);
    try {
      const response = await fetch(`/api/quizzes/${selectedQuizId}/questions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to fetch questions');
      }

      const data = await response.json();
      setQuestions(data || []);
      setError(null);
    } catch (err: any) {
      setError(err.message || "Failed to fetch questions.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQuestions();

    // Poll for updates every 5 seconds instead of realtime
    const interval = setInterval(() => {
      if (user && selectedQuizId) {
        fetchQuestions();
      }
    }, 5000);

    return () => {
      clearInterval(interval);
    };
  }, [user, selectedQuizId]);

  const handleQuestionSaved = () => {
    setEditingQuestion(null);
    fetchQuestions();
  };

  const handleCancelEdit = () => {
    setEditingQuestion(null);
  };

  const handleGenerationComplete = () => {
        fetchQuestions();
  };

  if (loading)
    return (
      <p className="text-slate-300">
        Loading questions for "{selectedQuizName}"...
      </p>
    );
  if (error) return <p className="text-red-400">Error: {error}</p>;

  return (
    <div className="mt-5 space-y-6">
      <div className="bg-slate-800 shadow-lg rounded-lg border border-slate-700 p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h4 className="text-2xl font-bold text-slate-200 mb-2">
              Manage Questions
            </h4>
            <p className="text-slate-400 text-sm">
              Quiz:{" "}
              <span className="text-purple-400 font-medium">
                {selectedQuizName}
              </span>
            </p>
          </div>
          <Button
            onClick={onClose}
            variant="outline"
            className="border-slate-600 text-slate-300 hover:text-white"
          >
            ← Back to Quizzes
          </Button>
        </div>

        {editingQuestion ? (
          <QuestionForm
            selectedQuizId={selectedQuizId}
            editingQuestion={editingQuestion}
            onQuestionSaved={handleQuestionSaved}
            onCancel={handleCancelEdit}
          />
        ) : (
          <QuestionForm
            selectedQuizId={selectedQuizId}
            onQuestionSaved={handleQuestionSaved}
            onCancel={handleCancelEdit}
          />
        )}

        <AiQuestionGenerator
          selectedQuizId={selectedQuizId}
          selectedQuizName={selectedQuizName}
          studyDocumentId={studyDocumentId}
          onGenerationComplete={handleGenerationComplete}
        />

        <QuestionsList
          questions={questions}
          loading={loading}
          onRefreshQuestions={fetchQuestions}
          selectedQuizId={selectedQuizId}
        />
      </div>
    </div>
  );
};
