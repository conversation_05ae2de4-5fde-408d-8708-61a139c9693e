import { createClient } from '@supabase/supabase-js';
import { supabaseConfig } from '../config';
import crypto from 'crypto';

// Create Supabase client for API key operations
const supabase = createClient(supabaseConfig.url, supabaseConfig.serviceRoleKey);

// Encryption key for API keys (should be from environment variable)
const ENCRYPTION_KEY = process.env.API_KEY_ENCRYPTION_KEY || 'default-key-change-in-production-32-chars';
const ALGORITHM = 'aes-256-cbc';

// Ensure the key is exactly 32 bytes for AES-256
const getEncryptionKey = (): Buffer => {
  if (typeof ENCRYPTION_KEY === 'string') {
    return crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
  }
  return ENCRYPTION_KEY;
};

/**
 * Encrypt an API key for secure storage
 */
function encryptApiKey(apiKey: string): { encrypted: string; iv: string; tag: string } {
  const iv = crypto.randomBytes(16);
  const key = getEncryptionKey();
  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

  let encrypted = cipher.update(apiKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  // For CBC mode, we don't have auth tag, so we'll use a hash for integrity
  const tag = crypto.createHmac('sha256', key).update(encrypted + iv.toString('hex')).digest('hex');

  return {
    encrypted,
    iv: iv.toString('hex'),
    tag
  };
}

/**
 * Decrypt an API key from storage
 */
function decryptApiKey(encryptedData: { encrypted: string; iv: string; tag: string }): string {
  const iv = Buffer.from(encryptedData.iv, 'hex');
  const key = getEncryptionKey();

  // Verify integrity using HMAC
  const expectedTag = crypto.createHmac('sha256', key).update(encryptedData.encrypted + encryptedData.iv).digest('hex');
  if (expectedTag !== encryptedData.tag) {
    throw new Error('Data integrity check failed');
  }

  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
}

/**
 * Store user's AI provider credentials securely in database
 */
export async function storeUserApiKey(
  userId: string, 
  provider: string, 
  apiKey: string, 
  baseUrl: string,
  models: { extraction: string; generation: string }
): Promise<{ success: boolean; error?: string }> {
  try {
    // Encrypt the API key
    const encryptedKey = encryptApiKey(apiKey);
    
    // Store in database with encryption metadata
    const { error } = await supabase
      .from('user_ai_credentials')
      .upsert({
        user_id: userId,
        provider,
        encrypted_api_key: encryptedKey.encrypted,
        encryption_iv: encryptedKey.iv,
        encryption_tag: encryptedKey.tag,
        base_url: baseUrl,
        extraction_model: models.extraction,
        generation_model: models.generation,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,provider'
      });

    if (error) {
      console.error('Error storing user API key:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in storeUserApiKey:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Retrieve and decrypt user's AI provider credentials
 */
export async function getUserApiKey(
  userId: string,
  provider: string
): Promise<{
  success: boolean;
  credentials?: {
    apiKey: string;
    baseUrl: string;
    extractionModel: string;
    generationModel: string;
  };
  error?: string;
}> {
  try {
    console.log(`🔍 [Credentials] Querying database for user_id: ${userId}, provider: ${provider}`);

    const { data, error } = await supabase
      .from('user_ai_credentials')
      .select('*')
      .eq('user_id', userId)
      .eq('provider', provider)
      .single();

    console.log(`🔍 [Credentials] Database query result:`, {
      hasData: !!data,
      hasError: !!error,
      errorCode: error?.code,
      errorMessage: error?.message,
      dataKeys: data ? Object.keys(data) : null
    });

    if (error) {
      if (error.code === 'PGRST116') {
        // No credentials found
        console.log(`📭 [Credentials] No credentials found for user ${userId} with provider ${provider}`);
        return { success: false, error: 'No credentials found for this provider' };
      }
      console.error('❌ [Credentials] Error retrieving user API key:', error);
      return { success: false, error: error.message };
    }

    // Decrypt the API key
    console.log(`🔓 [Credentials] Attempting to decrypt API key for user ${userId}`);
    try {
      const decryptedApiKey = decryptApiKey({
        encrypted: data.encrypted_api_key,
        iv: data.encryption_iv,
        tag: data.encryption_tag
      });

      console.log(`✅ [Credentials] Successfully decrypted credentials for user ${userId}:`, {
        hasApiKey: !!decryptedApiKey,
        apiKeyLength: decryptedApiKey?.length,
        baseUrl: data.base_url,
        extractionModel: data.extraction_model,
        generationModel: data.generation_model
      });

      return {
        success: true,
        credentials: {
          apiKey: decryptedApiKey,
          baseUrl: data.base_url,
          extractionModel: data.extraction_model,
          generationModel: data.generation_model
        }
      };
    } catch (decryptError: any) {
      console.error(`❌ [Credentials] Failed to decrypt API key for user ${userId}:`, decryptError);
      return { success: false, error: `Decryption failed: ${decryptError.message}` };
    }
  } catch (error: any) {
    console.error('❌ [Credentials] Error in getUserApiKey:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Delete user's stored API credentials
 */
export async function deleteUserApiKey(
  userId: string, 
  provider: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('user_ai_credentials')
      .delete()
      .eq('user_id', userId)
      .eq('provider', provider);

    if (error) {
      console.error('Error deleting user API key:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in deleteUserApiKey:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Get user credentials for ephemeral use (for AI API calls)
 * This function should be used only during AI API requests
 */
export async function getEphemeralUserCredentials(
  userId: string,
  provider: string
): Promise<{
  success: boolean;
  credentials?: {
    apiKey: string;
    baseUrl: string;
    extractionModel: string;
    generationModel: string;
  };
  error?: string;
}> {
  // This is the same as getUserApiKey but with explicit naming
  // to emphasize ephemeral usage
  return getUserApiKey(userId, provider);
}
