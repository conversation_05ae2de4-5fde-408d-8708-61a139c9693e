import { Hono, Context, Next } from "hono";
import { SupabaseClient } from "@supabase/supabase-js";
import { supabaseMiddleware } from "../middleware/supabaseMiddleware";
import {
  UserCompletionInsert,
  FlashcardSetCompletionData,
  CompletionStats,
  CompletionFilters,
} from "@shared/types/completion";

console.log("flashcardSetRoutes.ts: Module loaded");

type AppVariables = {
  supabase: SupabaseClient;
  user: { id: string };
};

const flashcardSetRoutes = new Hono<{ Variables: AppVariables }>();

// Apply the Supabase middleware to all routes
flashcardSetRoutes.use("*", supabaseMiddleware);

// Application-level error handler for flashcardSetRoutes
flashcardSetRoutes.onError((err, c) => {
  console.error("Error in flashcardSetRoutes:", err);
  return c.json(
    {
      error: "An unexpected error occurred in flashcard set routes.",
      message: err.message,
    },
    500
  );
});

// Middleware to ensure user is authenticated
const authMiddleware = async (
  c: Context<{ Variables: AppVariables }>,
  next: Next
) => {
  console.log(`flashcardSetRoutes: Auth middleware triggered for path: ${c.req.path}`);
  const authHeader = c.req.header("Authorization");

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    console.error("flashcardSetRoutes: Auth Error - Authorization header missing or malformed.");
    return c.json({ error: "Unauthorized: Missing or malformed token" }, 401);
  }

  const token = authHeader.split(" ")[1];
  if (!token) {
    console.error("flashcardSetRoutes: Auth Error - Token missing after Bearer split.");
    return c.json({ error: "Unauthorized: Missing token" }, 401);
  }

  const supabase = c.get("supabase");
  if (!supabase) {
    console.error("flashcardSetRoutes: Auth Error - Supabase client not found in request context.");
    return c.json(
      { error: "Server configuration error: Supabase client missing" },
      500
    );
  }

  console.log("flashcardSetRoutes: Auth - Token is present. Attempting supabase.auth.getUser(token).");
  try {
    const { data, error: getUserError } = await supabase.auth.getUser(token);

    if (getUserError) {
      console.error("flashcardSetRoutes: Auth Error - getUser failed:", getUserError.message);
      return c.json(
        { error: "Unauthorized: Invalid token", details: getUserError.message },
        401
      );
    }

    const user = data?.user;
    if (!user) {
      console.error("flashcardSetRoutes: Auth Error - No user found for token");
      return c.json({ error: "Unauthorized: No user found for token" }, 401);
    }

    console.log(`flashcardSetRoutes: Auth Success - User ${user.id} authenticated. Calling next().`);
    c.set("user", user);
    await next();
  } catch (err: any) {
    console.error("flashcardSetRoutes: Auth Fatal Error - Unexpected error in auth middleware try-catch block:", err.message, err.stack);
    return c.json(
      { error: "Internal server error during authentication processing" },
      500
    );
  }
};

flashcardSetRoutes.use("*", authMiddleware);

// Route to create a new flashcard set
flashcardSetRoutes.post("/", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");

  try {
    const body = await c.req.json();
    const { name, description, study_document_id, flashcards } = body;

    if (!name) {
      return c.json({ error: "Flashcard set name is required" }, 400);
    }

    // Create the flashcard set
    const { data: flashcardSet, error: setError } = await supabase
      .from("flashcard_sets")
      .insert({
        user_id: user.id,
        name: name,
        description: description || null,
        study_document_id: study_document_id || null,
      })
      .select("id, name, description, study_document_id, created_at")
      .single();

    if (setError) {
      console.error("Error creating flashcard set:", setError);
      return c.json(
        { error: "Failed to create flashcard set", details: setError.message },
        500
      );
    }

    if (!flashcardSet) {
      return c.json({ error: "No flashcard set returned from database" }, 500);
    }

    // If flashcards are provided, insert them
    if (flashcards && Array.isArray(flashcards) && flashcards.length > 0) {
      const flashcardsToInsert = flashcards.map((card: any) => ({
        set_id: flashcardSet.id,
        user_id: user.id,
        front_text: card.front_text || card.question || "",
        back_text: card.back_text || card.answer || "",
      }));

      const { error: cardsError } = await supabase
        .from("flashcards")
        .insert(flashcardsToInsert);

      if (cardsError) {
        console.error("Error creating flashcards:", cardsError);
        // Don't fail the whole operation, just log the error
        console.warn("Flashcard set created but flashcards failed to insert");
      }
    }

    return c.json(flashcardSet, 201);
  } catch (error: any) {
    console.error("Error in flashcard set creation:", error);
    return c.json({ error: error.message || "Failed to create flashcard set" }, 500);
  }
});

// Route to track flashcard set completion
flashcardSetRoutes.post(
  "/:setId/complete",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");
    const setId = c.req.param("setId");

    if (!setId) {
      return c.json({ error: "Flashcard set ID is required" }, 400);
    }

    try {
      const body = await c.req.json();
      const completionData: FlashcardSetCompletionData = body;

      // Validate required fields
      if (!completionData.time_spent_minutes) {
        return c.json({ error: "Time spent is required" }, 400);
      }

      // Verify the flashcard set exists (we don't need to check ownership since users can review any public sets)
      const { data: flashcardSet, error: setError } = await supabase
        .from("flashcard_sets")
        .select("id, user_id")
        .eq("id", setId)
        .single();

      if (setError || !flashcardSet) {
        return c.json({ error: "Flashcard set not found" }, 404);
      }

      // Create completion record
      const completionInsert: UserCompletionInsert = {
        user_id: user.id,
        flashcard_set_id: setId,
        completion_type: "flashcard_set",
        completed_at: new Date().toISOString(),
        time_spent_minutes: completionData.time_spent_minutes,
        metadata: completionData.metadata || undefined,
      };

      const { data: completion, error: insertError } = await supabase
        .from("user_completions")
        .insert(completionInsert)
        .select()
        .single();

      if (insertError) {
        console.error("Error recording flashcard set completion:", insertError);
        return c.json(
          { error: "Failed to record completion", details: insertError.message },
          500
        );
      }

      return c.json(completion, 201);
    } catch (error: any) {
      console.error("Error in flashcard set completion endpoint:", error);
      return c.json(
        { error: "An unexpected error occurred", details: error.message },
        500
      );
    }
  }
);

// Route to get all flashcard sets for the user
flashcardSetRoutes.get("/", async (c: Context<{ Variables: AppVariables }>) => {
  console.log("flashcardSetRoutes: GET / handler triggered");
  try {
    const supabase = c.get("supabase");
    const user = c.get("user");

    if (!supabase) {
      console.error("GET /api/flashcard-sets/ handler: Supabase client not found in context.");
      return c.json(
        {
          error: "Internal Server Configuration Error: Supabase client not available.",
        },
        500
      );
    }
    if (!user || !user.id) {
      console.error("GET /api/flashcard-sets/ handler: User not found in context or user ID missing.");
      return c.json(
        {
          error: "Authentication Error: User information not available.",
          details: "User context is invalid or missing.",
        },
        500
      );
    }

    console.log(`Fetching flashcard sets for user: ${user.id}`);

    // Get flashcard sets with card counts using a join query
    const { data: flashcardSets, error } = await supabase
      .from("flashcard_sets")
      .select(`
        *,
        flashcards(count)
      `)
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching flashcard sets:", error);
      return c.json(
        { error: "Failed to fetch flashcard sets", details: error.message },
        500
      );
    }

    // Transform the data to include card_count
    const setsWithCounts = (flashcardSets || []).map((set: any) => ({
      ...set,
      card_count: set.flashcards?.[0]?.count || 0,
      flashcards: undefined, // Remove the nested flashcards array
    }));

    console.log(`Found ${setsWithCounts.length} flashcard sets for user ${user.id}`);
    return c.json(setsWithCounts);
  } catch (error: any) {
    console.error("Error in flashcard sets fetch:", error);
    return c.json({ error: error.message || "Failed to fetch flashcard sets" }, 500);
  }
});

// Route to get a specific flashcard set with its flashcards
flashcardSetRoutes.get("/:setId", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");
  const setId = c.req.param("setId");

  if (!setId) {
    return c.json({ error: "Invalid flashcard set ID" }, 400);
  }

  try {
    // First get the flashcard set basic info with user ownership check
    const { data: flashcardSet, error: setError } = await supabase
      .from("flashcard_sets")
      .select("*")
      .eq("id", setId)
      .eq("user_id", user.id)
      .single();

    if (setError) {
      console.error("Error fetching flashcard set:", setError);
      if (setError.code === "PGRST116") {
        return c.json({ error: "Flashcard set not found or you don't have access" }, 404);
      }
      return c.json(
        { error: "Failed to fetch flashcard set", details: setError.message },
        500
      );
    }

    if (!flashcardSet) {
      return c.json({ error: "Flashcard set not found or you don't have access" }, 404);
    }

    // Get all flashcards for this set (with user ownership check)
    const { data: flashcards, error: flashcardsError } = await supabase
      .from("flashcards")
      .select("*")
      .eq("set_id", setId)
      .eq("user_id", user.id)
      .order("created_at", { ascending: true });

    if (flashcardsError) {
      console.error("Error fetching flashcards:", flashcardsError);
      return c.json(
        { error: "Failed to fetch flashcards", details: flashcardsError.message },
        500
      );
    }

    return c.json({
      ...flashcardSet,
      flashcards: flashcards || [],
    });
  } catch (error: any) {
    console.error("Error fetching flashcard set with flashcards:", error);
    return c.json({ error: error.message || "Failed to fetch flashcard set" }, 500);
  }
});

// Route to delete a flashcard set
flashcardSetRoutes.delete("/:setId", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");
  const setId = c.req.param("setId");

  if (!setId) {
    return c.json({ error: "Invalid flashcard set ID" }, 400);
  }

  try {
    // First verify the flashcard set exists and user owns it
    const { data: flashcardSet, error: setError } = await supabase
      .from("flashcard_sets")
      .select("id")
      .eq("id", setId)
      .eq("user_id", user.id)
      .single();

    if (setError) {
      console.error("Error checking flashcard set ownership:", setError);
      if (setError.code === "PGRST116") {
        return c.json({ error: "Flashcard set not found or you don't have access" }, 404);
      }
      return c.json(
        { error: "Failed to verify flashcard set", details: setError.message },
        500
      );
    }

    if (!flashcardSet) {
      return c.json({ error: "Flashcard set not found or you don't have access" }, 404);
    }

    // Delete all flashcards in the set first
    const { error: flashcardsError } = await supabase
      .from("flashcards")
      .delete()
      .eq("set_id", setId)
      .eq("user_id", user.id);

    if (flashcardsError) {
      console.error("Error deleting flashcards:", flashcardsError);
      return c.json(
        { error: "Failed to delete flashcards", details: flashcardsError.message },
        500
      );
    }

    // Delete the flashcard set
    const { error: deleteError } = await supabase
      .from("flashcard_sets")
      .delete()
      .eq("id", setId)
      .eq("user_id", user.id);

    if (deleteError) {
      console.error("Error deleting flashcard set:", deleteError);
      return c.json(
        { error: "Failed to delete flashcard set", details: deleteError.message },
        500
      );
    }

    return c.json({ message: "Flashcard set deleted successfully" }, 200);
  } catch (error: any) {
    console.error("Error deleting flashcard set:", error);
    return c.json({ error: error.message || "Failed to delete flashcard set" }, 500);
  }
});

// Route to add multiple flashcards to an existing flashcard set (batch insert)
flashcardSetRoutes.post("/:setId/flashcards/batch", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");
  const setId = c.req.param("setId");

  if (!setId) {
    return c.json({ error: "Invalid flashcard set ID" }, 400);
  }

  try {
    const body = await c.req.json();
    const { flashcards } = body;

    if (!flashcards || !Array.isArray(flashcards) || flashcards.length === 0) {
      return c.json({ error: "Flashcards array is required and must not be empty" }, 400);
    }

    // First verify the flashcard set exists and user owns it
    const { data: flashcardSet, error: setError } = await supabase
      .from("flashcard_sets")
      .select("id, user_id")
      .eq("id", setId)
      .eq("user_id", user.id)
      .single();

    if (setError) {
      console.error("Error checking flashcard set ownership:", setError);
      if (setError.code === "PGRST116") {
        return c.json({ error: "Flashcard set not found or you don't have access" }, 404);
      }
      return c.json(
        { error: "Failed to verify flashcard set", details: setError.message },
        500
      );
    }

    if (!flashcardSet) {
      return c.json({ error: "Flashcard set not found or you don't have access" }, 404);
    }

    // Prepare flashcards for insertion
    const flashcardsToInsert = flashcards.map((card: any) => ({
      set_id: setId,
      user_id: user.id,
      front_text: card.front_text || card.question || "",
      back_text: card.back_text || card.answer || "",
      created_at: new Date().toISOString(),
    }));

    // Insert flashcards in batch
    const { data: insertedFlashcards, error: insertError } = await supabase
      .from("flashcards")
      .insert(flashcardsToInsert)
      .select();

    if (insertError) {
      console.error("Error inserting flashcards:", insertError);
      return c.json(
        { error: "Failed to insert flashcards", details: insertError.message },
        500
      );
    }

    return c.json({
      message: `Successfully added ${insertedFlashcards?.length || 0} flashcards to the set`,
      flashcards: insertedFlashcards || [],
      count: insertedFlashcards?.length || 0,
    }, 201);
  } catch (error: any) {
    console.error("Error in batch flashcard insert:", error);
    return c.json({ error: error.message || "Failed to add flashcards to set" }, 500);
  }
});

// Route to get due flashcards for SRS review
flashcardSetRoutes.get("/:setId/due", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");
  const setId = c.req.param("setId");

  if (!setId) {
    return c.json({ error: "Invalid flashcard set ID" }, 400);
  }

  try {
    // Since the current flashcards table doesn't have SRS fields like due_at,
    // we'll return all flashcards for the set. In the future, we can add SRS fields
    // or implement a separate review tracking system.
    const { data: flashcards, error: flashcardsError } = await supabase
      .from("flashcards")
      .select("*")
      .eq("set_id", setId)
      .eq("user_id", user.id)
      .order("created_at", { ascending: true });

    if (flashcardsError) {
      console.error("Error fetching flashcards:", flashcardsError);
      return c.json(
        { error: "Failed to fetch flashcards", details: flashcardsError.message },
        500
      );
    }

    // For now, return all flashcards as "due" since we don't have SRS tracking yet
    return c.json(flashcards || []);
  } catch (error: any) {
    console.error("Error fetching due flashcards:", error);
    return c.json({ error: error.message || "Failed to fetch due flashcards" }, 500);
  }
});

// Route to get completion statistics for flashcard sets
flashcardSetRoutes.get(
  "/stats/completions",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");

    try {
      // Get all user completions for flashcard sets
      const { data: completions, error: completionsError } = await supabase
        .from("user_completions")
        .select("*")
        .eq("user_id", user.id)
        .eq("completion_type", "flashcard_set")
        .order("completed_at", { ascending: false });

      if (completionsError) {
        console.error("Error fetching flashcard set completions:", completionsError);
        return c.json(
          { error: "Failed to fetch completions", details: completionsError.message },
          500
        );
      }

      const allCompletions = completions || [];

      // Calculate statistics specific to flashcard sets
      const stats = {
        total_completions: allCompletions.length,
        total_study_time_minutes: allCompletions.reduce((sum, c) => sum + (c.time_spent_minutes || 0), 0),
        recent_completions: allCompletions.slice(0, 10),
        completion_streak: calculateCompletionStreak(allCompletions),
        this_week_completions: getCompletionsInTimeRange(allCompletions, 7),
        this_month_completions: getCompletionsInTimeRange(allCompletions, 30),
        average_cards_reviewed: allCompletions.reduce((sum, c) => {
          const cardsReviewed = c.metadata?.cards_reviewed || 0;
          return sum + cardsReviewed;
        }, 0) / (allCompletions.length || 1),
        average_session_time: allCompletions.reduce((sum, c) => sum + (c.time_spent_minutes || 0), 0) / (allCompletions.length || 1),
      };

      return c.json(stats);
    } catch (error: any) {
      console.error("Error fetching flashcard set completion stats:", error);
      return c.json(
        { error: "An unexpected error occurred", details: error.message },
        500
      );
    }
  }
);

// Route to get filtered completion history for flashcard sets
flashcardSetRoutes.get(
  "/completions",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");

    try {
      const queryParams = c.req.query();
      const filters: CompletionFilters = {
        completion_type: "flashcard_set", // Fixed to flashcard_set
        flashcard_set_id: queryParams.flashcard_set_id,
        limit: queryParams.limit ? parseInt(queryParams.limit) : 50,
        offset: queryParams.offset ? parseInt(queryParams.offset) : 0,
      };

      if (queryParams.start_date && queryParams.end_date) {
        filters.date_range = {
          start: queryParams.start_date,
          end: queryParams.end_date,
        };
      }

      // Build query
      let query = supabase
        .from("user_completions")
        .select("*")
        .eq("user_id", user.id)
        .eq("completion_type", "flashcard_set");

      if (filters.flashcard_set_id) {
        query = query.eq("flashcard_set_id", filters.flashcard_set_id);
      }

      if (filters.date_range) {
        query = query
          .gte("completed_at", filters.date_range.start)
          .lte("completed_at", filters.date_range.end);
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      query = query.order("completed_at", { ascending: false });

      const { data: completions, error: completionsError } = await query;

      if (completionsError) {
        console.error("Error fetching filtered flashcard set completions:", completionsError);
        return c.json(
          { error: "Failed to fetch completions", details: completionsError.message },
          500
        );
      }

      return c.json(completions || []);
    } catch (error: any) {
      console.error("Error in flashcard set completions endpoint:", error);
      return c.json(
        { error: "An unexpected error occurred", details: error.message },
        500
      );
    }
  }
);

// Helper functions for completion statistics
function calculateCompletionStreak(completions: any[]): number {
  if (completions.length === 0) return 0;

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  let streak = 0;
  let currentDate = new Date(today);
  
  for (let i = 0; i < 30; i++) { // Check last 30 days
    const dayCompletions = completions.filter(c => {
      const completionDate = new Date(c.completed_at);
      completionDate.setHours(0, 0, 0, 0);
      return completionDate.getTime() === currentDate.getTime();
    });
    
    if (dayCompletions.length > 0) {
      streak++;
    } else if (streak > 0) {
      break; // Streak is broken
    }
    
    currentDate.setDate(currentDate.getDate() - 1);
  }
  
  return streak;
}

function getCompletionsInTimeRange(completions: any[], days: number): number {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  
  return completions.filter(c => new Date(c.completed_at) >= cutoffDate).length;
}

// Custom Not Found handler for flashcardSetRoutes
flashcardSetRoutes.notFound((c) => {
  console.error(
    `[flashcardSetRoutes] Not Found: Path ${c.req.path} with method ${c.req.method} was not matched.`
  );
  return c.json(
    { error: "Flashcard set route not found", path: c.req.path, method: c.req.method },
    404
  );
});

export default flashcardSetRoutes;
