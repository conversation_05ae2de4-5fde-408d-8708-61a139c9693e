[["/home/<USER>/workspace/Makefile", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "<PERSON><PERSON><PERSON>"}}], ["/home/<USER>/workspace/env.example", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "env.example"}}], ["/home/<USER>/workspace/docs/SECURITY.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/SECURITY.md"}}], ["/home/<USER>/workspace/docs/RULES.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/RULES.md"}}], ["/home/<USER>/workspace/docs/README.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/README.md"}}], ["/home/<USER>/workspace/client/.env.example", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/.env.example"}}], ["/home/<USER>/workspace/client/src/contexts/QuizSettingsContext.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/contexts/QuizSettingsContext.tsx"}}], ["/home/<USER>/workspace/.env.example", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": ".env.example"}}], ["/home/<USER>/workspace/client/src/pages/AIConfig.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/AIConfig.tsx"}}], ["/home/<USER>/workspace/client/src/components/ai/AIConfigurationSection.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ai/AIConfigurationSection.tsx"}}], ["/home/<USER>/workspace/client/src/lib/ai-provider.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/ai-provider.ts"}}], ["/home/<USER>/workspace/docs/MEMORIES.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/MEMORIES.md"}}], ["/home/<USER>/workspace/.replit", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": ".replit"}}]]