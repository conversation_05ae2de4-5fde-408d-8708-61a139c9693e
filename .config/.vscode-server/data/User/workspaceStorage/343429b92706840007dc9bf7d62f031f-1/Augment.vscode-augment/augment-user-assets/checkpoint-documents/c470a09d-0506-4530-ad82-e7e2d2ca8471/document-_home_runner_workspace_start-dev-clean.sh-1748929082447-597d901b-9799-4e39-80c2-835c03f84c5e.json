{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "start-dev-clean.sh"}, "modifiedCode": "#!/bin/bash\n\n# Clean development startup script for Replit\necho \"🧹 Cleaning up existing processes...\"\n\n# Kill any existing Node.js processes that might be using our ports\npkill -f \"node.*server\" 2>/dev/null || true\npkill -f \"vite\" 2>/dev/null || true\npkill -f \"tsx\" 2>/dev/null || true\n\n# Wait a moment for processes to clean up\nsleep 2\n\necho \"🚀 Starting development servers...\"\n\n# Start the development servers\nnpm run dev\n"}