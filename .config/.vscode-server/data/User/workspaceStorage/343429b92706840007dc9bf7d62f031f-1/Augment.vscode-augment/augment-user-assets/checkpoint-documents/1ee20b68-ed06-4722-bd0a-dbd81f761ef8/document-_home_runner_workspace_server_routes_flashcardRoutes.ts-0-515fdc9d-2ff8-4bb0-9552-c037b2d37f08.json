{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/flashcardRoutes.ts"}, "originalCode": "import express, { Request, Response } from \"express\";\nimport { db } from \"../db/drizzle\";\nimport { flashcards, insertFlashcardSchema, flashcardDecks } from \"../../shared/schema\";\nimport { eq, and } from \"drizzle-orm\";\nimport { supabaseClient } from \"../middleware/supabaseMiddleware\";\n\nconst router = express.Router();\n\n// Helper function to verify the current user is authenticated\nasync function getAuthenticatedUser(req: Request): Promise<\n  | { error: string; details?: string; status: number }\n  | { user: { id: string; [key: string]: any } }\n> {\n  try {\n    const authHeader = req.headers.authorization;\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n      return { error: \"Unauthorized: Missing or malformed token\", status: 401 };\n    }\n    const token = authHeader.split(\" \")[1];\n    if (!token) {\n      return { error: \"Unauthorized: Missing token\", status: 401 };\n    }\n    const supabase = supabaseClient;\n    const { data, error: getUserError } = await supabase.auth.getUser(token);\n    if (getUserError) {\n      if (\n        getUserError.message.toLowerCase().includes(\"invalid token\") ||\n        getUserError.message.includes(\"jwt\")\n      ) {\n        return {\n          error: \"Unauthorized: Invalid token\",\n          details: getUserError.message,\n          status: 401,\n        };\n      }\n      return {\n        error: \"Server error validating token\",\n        details: getUserError.message,\n        status: 500,\n      };\n    }\n    const user = data?.user;\n    if (!user) {\n      return { error: \"Unauthorized: No user found for token\", status: 401 };\n    }\n    if (!user.id) {\n      return { error: \"User ID missing from authenticated user\", status: 500 };\n    }\n    return { user: { ...user } };\n  } catch (err: any) {\n    console.error(\"Auth error in flashcardRoutes:\", err.message, err.stack);\n    return { error: \"Authentication error\", status: 500 };\n  }\n}\n\n// POST /api/decks/:deckId/flashcards - Create a new flashcard in a deck\nrouter.post(\"/decks/:deckId/flashcards\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n  const { deckId } = req.params;\n  try {\n    // Verify deck ownership\n    const deck = await db\n      .select()\n      .from(flashcardDecks)\n      .where(\n        and(\n          eq(flashcardDecks.id, Number(deckId)),\n          eq(flashcardDecks.userId, user.id)\n        )\n      );\n    if (!deck || deck.length === 0) {\n      return res.status(404).json({ error: \"Deck not found or not owned by user\" });\n    }\n    const body = req.body;\n    const parsedBody = insertFlashcardSchema\n      .omit({ deckId: true })\n      .safeParse(body);\n    if (!parsedBody.success) {\n      return res\n        .status(400)\n        .json({ error: \"Invalid flashcard data\", details: parsedBody.error.flatten() });\n    }\n    const cardData = {\n      ...parsedBody.data,\n      deckId: Number(deckId),\n      createdAt: Date.now(),\n    };\n    const newCard = await db.insert(flashcards).values(cardData).returning();\n    if (!newCard || newCard.length === 0) {\n      return res.status(500).json({ error: \"Failed to create flashcard\" });\n    }\n    return res.status(201).json(newCard[0]);\n  } catch (error: any) {\n    console.error(\"Error creating flashcard:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to create flashcard\", details: error.message });\n  }\n});\n\n// GET /api/decks/:deckId/flashcards - Get all flashcards for a deck\nrouter.get(\"/decks/:deckId/flashcards\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n  const { deckId } = req.params;\n  try {\n    // Verify deck ownership\n    const deck = await db\n      .select()\n      .from(flashcardDecks)\n      .where(\n        and(\n          eq(flashcardDecks.id, Number(deckId)),\n          eq(flashcardDecks.userId, user.id)\n        )\n      );\n    if (!deck || deck.length === 0) {\n      return res.status(404).json({ error: \"Deck not found or not owned by user\" });\n    }\n    const cards = await db\n      .select()\n      .from(flashcards)\n      .where(eq(flashcards.deckId, Number(deckId)));\n    return res.status(200).json(cards);\n  } catch (error: any) {\n    console.error(\"Error fetching flashcards:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to fetch flashcards\", details: error.message });\n  }\n});\n\nexport default router;\n", "modifiedCode": "import express, { Request, Response } from \"express\";\nimport { db } from \"../db/drizzle\";\nimport { flashcards, insertFlashcardSchema, flashcardDecks } from \"../../shared/schema\";\nimport { eq, and } from \"drizzle-orm\";\nimport { supabaseClient } from \"../middleware/supabaseMiddleware\";\n\nconst router = express.Router();\n\n// Helper function to verify the current user is authenticated\nasync function getAuthenticatedUser(req: Request): Promise<\n  | { error: string; details?: string; status: number }\n  | { user: { id: string; [key: string]: any } }\n> {\n  try {\n    const authHeader = req.headers.authorization;\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n      return { error: \"Unauthorized: Missing or malformed token\", status: 401 };\n    }\n    const token = authHeader.split(\" \")[1];\n    if (!token) {\n      return { error: \"Unauthorized: Missing token\", status: 401 };\n    }\n    const supabase = supabaseClient;\n    const { data, error: getUserError } = await supabase.auth.getUser(token);\n    if (getUserError) {\n      if (\n        getUserError.message.toLowerCase().includes(\"invalid token\") ||\n        getUserError.message.includes(\"jwt\")\n      ) {\n        return {\n          error: \"Unauthorized: Invalid token\",\n          details: getUserError.message,\n          status: 401,\n        };\n      }\n      return {\n        error: \"Server error validating token\",\n        details: getUserError.message,\n        status: 500,\n      };\n    }\n    const user = data?.user;\n    if (!user) {\n      return { error: \"Unauthorized: No user found for token\", status: 401 };\n    }\n    if (!user.id) {\n      return { error: \"User ID missing from authenticated user\", status: 500 };\n    }\n    return { user: { ...user } };\n  } catch (err: any) {\n    console.error(\"Auth error in flashcardRoutes:\", err.message, err.stack);\n    return { error: \"Authentication error\", status: 500 };\n  }\n}\n\n// POST /api/decks/:deckId/flashcards - Create a new flashcard in a deck\nrouter.post(\"/decks/:deckId/flashcards\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n  const { deckId } = req.params;\n  try {\n    // Verify deck ownership\n    const deck = await db\n      .select()\n      .from(flashcardDecks)\n      .where(\n        and(\n          eq(flashcardDecks.id, Number(deckId)),\n          eq(flashcardDecks.userId, user.id)\n        )\n      );\n    if (!deck || deck.length === 0) {\n      return res.status(404).json({ error: \"Deck not found or not owned by user\" });\n    }\n    const body = req.body;\n    const parsedBody = insertFlashcardSchema\n      .omit({ deckId: true })\n      .safeParse(body);\n    if (!parsedBody.success) {\n      return res\n        .status(400)\n        .json({ error: \"Invalid flashcard data\", details: parsedBody.error.flatten() });\n    }\n    const cardData = {\n      ...parsedBody.data,\n      deckId: Number(deckId),\n      createdAt: Date.now(),\n    };\n    const newCard = await db.insert(flashcards).values(cardData).returning();\n    if (!newCard || newCard.length === 0) {\n      return res.status(500).json({ error: \"Failed to create flashcard\" });\n    }\n    return res.status(201).json(newCard[0]);\n  } catch (error: any) {\n    console.error(\"Error creating flashcard:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to create flashcard\", details: error.message });\n  }\n});\n\n// GET /api/decks/:deckId/flashcards - Get all flashcards for a deck\nrouter.get(\"/decks/:deckId/flashcards\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n  const { deckId } = req.params;\n  try {\n    // Verify deck ownership\n    const deck = await db\n      .select()\n      .from(flashcardDecks)\n      .where(\n        and(\n          eq(flashcardDecks.id, Number(deckId)),\n          eq(flashcardDecks.userId, user.id)\n        )\n      );\n    if (!deck || deck.length === 0) {\n      return res.status(404).json({ error: \"Deck not found or not owned by user\" });\n    }\n    const cards = await db\n      .select()\n      .from(flashcards)\n      .where(eq(flashcards.deckId, Number(deckId)));\n    return res.status(200).json(cards);\n  } catch (error: any) {\n    console.error(\"Error fetching flashcards:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to fetch flashcards\", details: error.message });\n  }\n});\n\nexport default router;\n"}