{"path": {"rootPath": "/home/<USER>/workspace", "relPath": ".env"}, "originalCode": "# =============================================================================\n# ChewyAI Environment Configuration\n# =============================================================================\n\n# =============================================================================\n# CLIENT ENVIRONMENT VARIABLES (VITE_ prefix for Vite)\n# =============================================================================\n# These are exposed to the browser - only include non-sensitive values\n\n# Supabase Configuration (Client)\nVITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nVITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0qS0Nh1aV4GjZYniANoUZKDDcuCcQxzTm-DnTeZsNlQ\n\n# API Configuration\nVITE_API_BASE_URL=http://localhost:5000/api\n\n# =============================================================================\n# SERVER ENVIRONMENT VARIABLES\n# =============================================================================\n# These are only available on the server - include sensitive values here\n\n# Application Configuration\nNODE_ENV=development\nPORT=5000\n\n# Supabase Configuration (Server)\nSUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nSUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8\n\n# Database Configuration\nVITE_DATABASE_PASSWORD=your-database-password-here\n\n# =============================================================================\n# AI PROVIDER CONFIGURATION (Optional)\n# =============================================================================\nDEFAULT_AI_PROVIDER=openrouter\nDEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1\nDEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash-preview-05-20\nDEFAULT_GENERATION_MODEL=google/gemini-2.5-pro-preview\n\n# =============================================================================\n# SECURITY CONFIGURATION\n# =============================================================================\nJWT_SECRET=your-jwt-secret-change-this-in-production-make-it-very-long-and-random\nSESSION_SECRET=your-session-secret-change-this-in-production-make-it-very-long-and-random\n\n# =============================================================================\n# CORS CONFIGURATION\n# =============================================================================\nFRONTEND_URL=http://localhost:3000\n\n# =============================================================================\n# ADDITIONAL DATABASE CONFIGURATION (Optional)\n# =============================================================================\nDATABASE_URL=./data/chewyai.sqlite\n", "modifiedCode": "# =============================================================================\n# ChewyAI Environment Configuration\n# =============================================================================\n\n# =============================================================================\n# CLIENT ENVIRONMENT VARIABLES (VITE_ prefix for Vite)\n# =============================================================================\n# These are exposed to the browser - only include non-sensitive values\n\n# Supabase Configuration (Client)\nVITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nVITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0qS0Nh1aV4GjZYniANoUZKDDcuCcQxzTm-DnTeZsNlQ\n\n# API Configuration\n# In development: use localhost:5000\n# In production: use relative path (same origin)\nVITE_API_BASE_URL=http://localhost:5000/api\n\n# =============================================================================\n# SERVER ENVIRONMENT VARIABLES\n# =============================================================================\n# These are only available on the server - include sensitive values here\n\n# Application Configuration\nNODE_ENV=development\nPORT=5000\n\n# Supabase Configuration (Server)\nSUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nSUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8\n\n# Database Configuration\nVITE_DATABASE_PASSWORD=your-database-password-here\n\n# =============================================================================\n# AI PROVIDER CONFIGURATION (Optional)\n# =============================================================================\nDEFAULT_AI_PROVIDER=openrouter\nDEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1\nDEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash-preview-05-20\nDEFAULT_GENERATION_MODEL=google/gemini-2.5-pro-preview\n\n# =============================================================================\n# SECURITY CONFIGURATION\n# =============================================================================\nJWT_SECRET=your-jwt-secret-change-this-in-production-make-it-very-long-and-random\nSESSION_SECRET=your-session-secret-change-this-in-production-make-it-very-long-and-random\n\n# =============================================================================\n# CORS CONFIGURATION\n# =============================================================================\nFRONTEND_URL=http://localhost:3000\n\n# =============================================================================\n# ADDITIONAL DATABASE CONFIGURATION (Optional)\n# =============================================================================\nDATABASE_URL=./data/chewyai.sqlite\n"}