{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "vite.config.ts"}, "originalCode": "import { defineConfig } from \"vite\";\nimport react from \"@vitejs/plugin-react\";\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\nimport runtimeErrorOverlay from \"@replit/vite-plugin-runtime-error-modal\";\n\n// Get current file's directory in ES modules\nconst __dirname = path.dirname(fileURLToPath(import.meta.url));\n\nexport default defineConfig({\n  plugins: [\n    react(),\n    runtimeErrorOverlay(),\n    ...(process.env.NODE_ENV !== \"production\" &&\n    process.env.REPL_ID !== undefined\n      ? [\n          await import(\"@replit/vite-plugin-cartographer\").then((m) =>\n            m.cartographer(),\n          ),\n        ]\n      : []),\n  ],\n  resolve: {\n    alias: {\n      \"@\": path.resolve(__dirname, \"client\", \"src\"),\n      \"@shared\": path.resolve(__dirname, \"shared\"),\n      \"@assets\": path.resolve(__dirname, \"attached_assets\"),\n    },\n  },\n  root: path.resolve(__dirname, \"client\"),\n  build: {\n    outDir: path.resolve(__dirname, \"dist/public\"),\n    emptyOutDir: true,\n    // Production optimizations\n    minify: 'esbuild',\n    sourcemap: process.env.NODE_ENV !== 'production',\n    rollupOptions: {\n      output: {\n        // Optimize chunk splitting for better caching\n        manualChunks: {\n          vendor: ['react', 'react-dom'],\n          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],\n          router: ['wouter'],\n          query: ['@tanstack/react-query'],\n        },\n        // Add hash to filenames for cache busting\n        chunkFileNames: 'assets/[name]-[hash].js',\n        entryFileNames: 'assets/[name]-[hash].js',\n        assetFileNames: 'assets/[name]-[hash].[ext]'\n      }\n    },\n    // Increase chunk size warning limit for production\n    chunkSizeWarningLimit: 1000,\n  },\n  server: {\n    host: \"0.0.0.0\", // Allow external connections in Docker\n    port: 3000,\n    proxy: {\n      \"/api\": {\n        target: \"http://localhost:5000\",\n        changeOrigin: true,\n        secure: false,\n      },\n    },\n    // Add the allowedHosts configuration here\n    allowedHosts: [\n      \"304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev\",\n      \"chewy-ai.replit.app\",\n      // It's good practice to also keep localhost and 127.0.0.1 if you use them directly\n      \"localhost\",\n      \"127.0.0.1\",\n    ],\n  },\n});\n", "modifiedCode": "import { defineConfig } from \"vite\";\nimport react from \"@vitejs/plugin-react\";\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\nimport runtimeErrorOverlay from \"@replit/vite-plugin-runtime-error-modal\";\n\n// Get current file's directory in ES modules\nconst __dirname = path.dirname(fileURLToPath(import.meta.url));\n\nexport default defineConfig({\n  plugins: [\n    react(),\n    runtimeErrorOverlay(),\n    ...(process.env.NODE_ENV !== \"production\" &&\n    process.env.REPL_ID !== undefined\n      ? [\n          await import(\"@replit/vite-plugin-cartographer\").then((m) =>\n            m.cartographer(),\n          ),\n        ]\n      : []),\n  ],\n  resolve: {\n    alias: {\n      \"@\": path.resolve(__dirname, \"client\", \"src\"),\n      \"@shared\": path.resolve(__dirname, \"shared\"),\n      \"@assets\": path.resolve(__dirname, \"attached_assets\"),\n    },\n  },\n  root: path.resolve(__dirname, \"client\"),\n  build: {\n    outDir: path.resolve(__dirname, \"dist/public\"),\n    emptyOutDir: true,\n    // Production optimizations\n    minify: 'esbuild',\n    sourcemap: process.env.NODE_ENV !== 'production',\n    rollupOptions: {\n      output: {\n        // Optimize chunk splitting for better caching\n        manualChunks: {\n          vendor: ['react', 'react-dom'],\n          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],\n          router: ['wouter'],\n          query: ['@tanstack/react-query'],\n        },\n        // Add hash to filenames for cache busting\n        chunkFileNames: 'assets/[name]-[hash].js',\n        entryFileNames: 'assets/[name]-[hash].js',\n        assetFileNames: 'assets/[name]-[hash].[ext]'\n      }\n    },\n    // Increase chunk size warning limit for production\n    chunkSizeWarningLimit: 1000,\n  },\n  server: {\n    host: \"0.0.0.0\", // Allow external connections in Docker\n    port: 3000,\n    proxy: {\n      \"/api\": {\n        target: \"http://localhost:5000\",\n        changeOrigin: true,\n        secure: false,\n      },\n    },\n    // Add the allowedHosts configuration here\n    allowedHosts: [\n      \"304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev\",\n      \"chewy-ai.replit.app\",\n      // It's good practice to also keep localhost and 127.0.0.1 if you use them directly\n      \"localhost\",\n      \"127.0.0.1\",\n    ],\n    // Fix HMR WebSocket connection issues\n    hmr: {\n      port: 24679, // Use a different port to avoid conflicts\n      host: \"localhost\",\n    },\n  },\n});\n"}