{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/dashboard/DashboardOverview.tsx"}, "originalCode": "import React from \"react\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { getStudyStats } from \"@/lib/storage\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { FileText, BookOpen, FileQuestion, Trophy } from \"lucide-react\";\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  description: string;\n  icon: React.ReactElement;\n  isLoading?: boolean;\n}\n\nconst StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  description,\n  icon,\n  isLoading,\n}) => {\n  const cardClasses =\n    \"bg-slate-800 border-slate-700 text-purple-400 shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 group\";\n  const iconElementClasses = \"h-5 w-5 text-purple-400\";\n\n  if (isLoading) {\n    return (\n      <Card className={cardClasses}>\n        <CardContent className=\"p-5 flex flex-col space-y-2\">\n          <div className=\"flex items-center justify-between w-full\">\n            <Skeleton className=\"h-5 w-3/5 bg-slate-700\" />\n            <Skeleton className=\"h-9 w-9 rounded-lg bg-slate-700\" />\n          </div>\n          <Skeleton className=\"h-8 w-2/5 bg-slate-700\" />\n          <Skeleton className=\"h-4 w-4/5 bg-slate-700\" />\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const titleClasses = \"text-base font-medium text-slate-300\";\n  const valueClasses =\n    \"text-2xl font-bold text-purple-400 group-hover:text-purple-300 transition-colors\";\n  const descriptionClasses =\n    \"text-xs text-slate-400 group-hover:text-slate-300 transition-colors\";\n  const iconContainerClasses =\n    \"p-2 bg-purple-500/20 group-hover:bg-purple-500/30 transition-colors rounded-lg flex items-center justify-center\";\n\n  return (\n    <Card className={`${cardClasses} group`}>\n      <CardContent className=\"p-5 flex flex-col space-y-2\">\n        <div className=\"flex items-start justify-between w-full\">\n          <h3 className={titleClasses}>{title}</h3>\n          <div className={iconContainerClasses}>\n            {React.cloneElement(icon, { className: iconElementClasses })}\n          </div>\n        </div>\n        <p className={valueClasses}>{value}</p>\n        <p className={descriptionClasses}>{description}</p>\n      </CardContent>\n    </Card>\n  );\n};\n\nconst DashboardOverview: React.FC = () => {\n  const { data: stats, isLoading } = useQuery({\n    queryKey: [\"/api/study-stats\"],\n    queryFn: getStudyStats,\n  });\n\n  const overviewItems = [\n    {\n      title: \"Documents\",\n      getValue: (s: any) => s?.totalDocuments || 0,\n      description: \"Total uploaded\",\n      icon: <FileText />,\n    },\n    {\n      title: \"Total Flashcards\",\n      getValue: (s: any) => s?.totalFlashcards || \"0\",\n      description: \"All flashcards created\",\n      icon: <BookOpen />,\n    },\n    {\n      title: \"Total Quizzes\",\n      getValue: (s: any) => s?.totalQuizQuestions || \"0\",\n      description: \"Questions across all quizzes\",\n      icon: <FileQuestion />,\n    },\n    {\n      title: \"Completions\",\n      getValue: (s: any) => s?.totalCompletions || \"0\",\n      description: \"Quizzes/sets completed\",\n      icon: <Trophy />,\n    },\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n        {[...Array(4)].map((_, i) => (\n          <StatCard\n            key={`skeleton-${i}`}\n            title=\"\"\n            value=\"\"\n            description=\"\"\n            icon={<div />}\n            isLoading={true}\n          />\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n      {overviewItems.map((item) => (\n        <StatCard\n          key={item.title}\n          title={item.title}\n          value={item.getValue(stats)}\n          description={item.description}\n          icon={item.icon}\n          isLoading={false}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default DashboardOverview;\n", "modifiedCode": "import React from \"react\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { getDashboardStatsAPI } from \"@/lib/api\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { FileTex<PERSON>, BookOpen, FileQuestion, Trophy } from \"lucide-react\";\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  description: string;\n  icon: React.ReactElement;\n  isLoading?: boolean;\n}\n\nconst StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  description,\n  icon,\n  isLoading,\n}) => {\n  const cardClasses =\n    \"bg-slate-800 border-slate-700 text-purple-400 shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 group\";\n  const iconElementClasses = \"h-5 w-5 text-purple-400\";\n\n  if (isLoading) {\n    return (\n      <Card className={cardClasses}>\n        <CardContent className=\"p-5 flex flex-col space-y-2\">\n          <div className=\"flex items-center justify-between w-full\">\n            <Skeleton className=\"h-5 w-3/5 bg-slate-700\" />\n            <Skeleton className=\"h-9 w-9 rounded-lg bg-slate-700\" />\n          </div>\n          <Skeleton className=\"h-8 w-2/5 bg-slate-700\" />\n          <Skeleton className=\"h-4 w-4/5 bg-slate-700\" />\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const titleClasses = \"text-base font-medium text-slate-300\";\n  const valueClasses =\n    \"text-2xl font-bold text-purple-400 group-hover:text-purple-300 transition-colors\";\n  const descriptionClasses =\n    \"text-xs text-slate-400 group-hover:text-slate-300 transition-colors\";\n  const iconContainerClasses =\n    \"p-2 bg-purple-500/20 group-hover:bg-purple-500/30 transition-colors rounded-lg flex items-center justify-center\";\n\n  return (\n    <Card className={`${cardClasses} group`}>\n      <CardContent className=\"p-5 flex flex-col space-y-2\">\n        <div className=\"flex items-start justify-between w-full\">\n          <h3 className={titleClasses}>{title}</h3>\n          <div className={iconContainerClasses}>\n            {React.cloneElement(icon, { className: iconElementClasses })}\n          </div>\n        </div>\n        <p className={valueClasses}>{value}</p>\n        <p className={descriptionClasses}>{description}</p>\n      </CardContent>\n    </Card>\n  );\n};\n\nconst DashboardOverview: React.FC = () => {\n  const { data: stats, isLoading } = useQuery({\n    queryKey: [\"/api/study-stats\"],\n    queryFn: getStudyStats,\n  });\n\n  const overviewItems = [\n    {\n      title: \"Documents\",\n      getValue: (s: any) => s?.totalDocuments || 0,\n      description: \"Total uploaded\",\n      icon: <FileText />,\n    },\n    {\n      title: \"Total Flashcards\",\n      getValue: (s: any) => s?.totalFlashcards || \"0\",\n      description: \"All flashcards created\",\n      icon: <BookOpen />,\n    },\n    {\n      title: \"Total Quizzes\",\n      getValue: (s: any) => s?.totalQuizQuestions || \"0\",\n      description: \"Questions across all quizzes\",\n      icon: <FileQuestion />,\n    },\n    {\n      title: \"Completions\",\n      getValue: (s: any) => s?.totalCompletions || \"0\",\n      description: \"Quizzes/sets completed\",\n      icon: <Trophy />,\n    },\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n        {[...Array(4)].map((_, i) => (\n          <StatCard\n            key={`skeleton-${i}`}\n            title=\"\"\n            value=\"\"\n            description=\"\"\n            icon={<div />}\n            isLoading={true}\n          />\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n      {overviewItems.map((item) => (\n        <StatCard\n          key={item.title}\n          title={item.title}\n          value={item.getValue(stats)}\n          description={item.description}\n          icon={item.icon}\n          isLoading={false}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default DashboardOverview;\n"}