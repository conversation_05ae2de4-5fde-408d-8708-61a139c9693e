{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuizQuestionManager.tsx"}, "originalCode": "import React, { useEffect, useState, useRef, useCallback } from \"react\";\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables } from \"../../types/supabase\";\nimport { QuestionForm } from \"./QuestionForm\";\nimport { AiQuestionGenerator } from \"./AiQuestionGenerator\";\nimport { QuestionsList, QuestionsListProps } from \"./QuestionsList\";\nimport { Button } from \"@/components/ui/button\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\ninterface QuizQuestionManagerProps {\n  selectedQuizId: string;\n  selectedQuizName: string;\n  studyDocumentId?: string;\n  onClose: () => void;\n}\n\nexport const QuizQuestionManager: React.FC<QuizQuestionManagerProps> = ({\n  selectedQuizId,\n  selectedQuizName,\n  studyDocumentId,\n  onClose,\n}) => {\n  const { user, signOut } = useAuth();\n  const [questions, setQuestions] = useState<QuizQuestion[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(\n    null\n  );\n\n  // Refs to manage polling and prevent memory leaks\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const mountedRef = useRef(true);\n  const retryCountRef = useRef(0);\n  const abortControllerRef = useRef<AbortController | null>(null);\n  const consecutiveFailuresRef = useRef(0);\n  const lastSuccessTimeRef = useRef(Date.now());\n  const maxRetries = 3;\n  const maxConsecutiveFailures = 5; // Circuit breaker threshold\n  const pollingInterval = 15000; // Increased to 15 seconds to reduce load\n\n  // Clear polling interval and abort any ongoing requests\n  const clearPolling = useCallback(() => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n\n  // Handle authentication errors - simplified to avoid dependency cycles\n  const handleAuthError = useCallback(async () => {\n    console.warn('Authentication failed in QuizQuestionManager, signing out...');\n    // Clear polling first\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n\n    try {\n      await signOut();\n    } catch (error) {\n      console.error('Error during signOut:', error);\n    }\n  }, [signOut]); // Removed clearPolling dependency to break cycle\n\n  const fetchQuestions = useCallback(async (isRetry = false) => {\n    if (!user || !selectedQuizId || !mountedRef.current) return;\n\n    // Circuit breaker: Stop making requests if too many consecutive failures\n    if (consecutiveFailuresRef.current >= maxConsecutiveFailures) {\n      const timeSinceLastSuccess = Date.now() - lastSuccessTimeRef.current;\n      const cooldownPeriod = 60000; // 1 minute cooldown\n\n      if (timeSinceLastSuccess < cooldownPeriod) {\n        console.warn(`Circuit breaker active: ${consecutiveFailuresRef.current} consecutive failures. Waiting ${Math.ceil((cooldownPeriod - timeSinceLastSuccess) / 1000)}s before retry.`);\n        return;\n      } else {\n        // Reset circuit breaker after cooldown\n        console.log('Circuit breaker cooldown complete, resetting failure count');\n        consecutiveFailuresRef.current = 0;\n      }\n    }\n\n    // Cancel any previous request\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n    }\n\n    // Create new abort controller for this request\n    abortControllerRef.current = new AbortController();\n    const signal = abortControllerRef.current.signal;\n\n    // Don't show loading on retries to avoid UI flicker\n    if (!isRetry) {\n      setLoading(true);\n    }\n\n    try {\n      const token = localStorage.getItem('auth_token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const response = await fetch(`/api/quizzes/${selectedQuizId}/questions`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n        signal, // Add abort signal to request\n      });\n\n      if (!response.ok) {\n        if (response.status === 401) {\n          // Authentication failed - handle it properly\n          await handleAuthError();\n          return;\n        }\n\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch questions`);\n      }\n\n      const data = await response.json();\n\n      if (mountedRef.current && !signal.aborted) {\n        setQuestions(data || []);\n        setError(null);\n        retryCountRef.current = 0; // Reset retry count on success\n      }\n    } catch (err: any) {\n      // Don't log errors for aborted requests\n      if (err.name === 'AbortError') {\n        console.log('Request was cancelled');\n        return;\n      }\n\n      console.error('Error fetching questions:', err);\n\n      if (mountedRef.current && !signal.aborted) {\n        // Only set error if we're still mounted and it's not an auth error or network error\n        if (!err.message?.includes('authentication') &&\n            !err.message?.includes('token') &&\n            !err.message?.includes('Failed to fetch')) {\n          setError(err.message || \"Failed to fetch questions.\");\n\n          // Implement exponential backoff for retries (only for non-auth errors)\n          if (retryCountRef.current < maxRetries) {\n            retryCountRef.current++;\n            const delay = Math.pow(2, retryCountRef.current) * 2000; // 4s, 8s, 16s\n            console.log(`Retrying in ${delay}ms (attempt ${retryCountRef.current}/${maxRetries})`);\n\n            setTimeout(() => {\n              if (mountedRef.current) {\n                fetchQuestions(true);\n              }\n            }, delay);\n          }\n        } else if (err.message?.includes('Failed to fetch')) {\n          // Network error - stop polling to prevent spam\n          console.warn('Network error detected, stopping polling to prevent request spam');\n          if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n            intervalRef.current = null;\n          }\n          setError(\"Network connection lost. Please refresh the page to retry.\");\n        }\n      }\n    } finally {\n      if (mountedRef.current && !isRetry && !signal.aborted) {\n        setLoading(false);\n      }\n    }\n  }, [user, selectedQuizId, handleAuthError]);\n\n  // Simplified useEffect to break dependency cycles\n  useEffect(() => {\n    // Mark as mounted\n    mountedRef.current = true;\n\n    // Initial fetch\n    if (user && selectedQuizId) {\n      fetchQuestions();\n\n      // Start polling after a delay to allow initial fetch to complete\n      const startPollingTimer = setTimeout(() => {\n        if (mountedRef.current && user && selectedQuizId) {\n          intervalRef.current = setInterval(() => {\n            if (mountedRef.current && user && selectedQuizId) {\n              fetchQuestions(true); // Mark as retry to avoid loading state\n            }\n          }, pollingInterval);\n        }\n      }, 2000); // 2 second delay before starting polling\n\n      return () => {\n        clearTimeout(startPollingTimer);\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n          intervalRef.current = null;\n        }\n        if (abortControllerRef.current) {\n          abortControllerRef.current.abort();\n          abortControllerRef.current = null;\n        }\n      };\n    }\n  }, [user, selectedQuizId]); // Simplified dependencies - only the essential ones\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      mountedRef.current = false;\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n      if (abortControllerRef.current) {\n        abortControllerRef.current.abort();\n        abortControllerRef.current = null;\n      }\n    };\n  }, []); // Empty dependency array for cleanup only\n\n  const handleQuestionSaved = useCallback(() => {\n    setEditingQuestion(null);\n    fetchQuestions();\n  }, [fetchQuestions]);\n\n  const handleCancelEdit = useCallback(() => {\n    setEditingQuestion(null);\n  }, []);\n\n  const handleGenerationComplete = useCallback(() => {\n    fetchQuestions();\n  }, [fetchQuestions]);\n\n  if (loading)\n    return (\n      <p className=\"text-slate-300\">\n        Loading questions for \"{selectedQuizName}\"...\n      </p>\n    );\n  if (error) return <p className=\"text-red-400\">Error: {error}</p>;\n\n  return (\n    <div className=\"mt-5 space-y-6\">\n      <div className=\"bg-slate-800 shadow-lg rounded-lg border border-slate-700 p-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <div>\n            <h4 className=\"text-2xl font-bold text-slate-200 mb-2\">\n              Manage Questions\n            </h4>\n            <p className=\"text-slate-400 text-sm\">\n              Quiz:{\" \"}\n              <span className=\"text-purple-400 font-medium\">\n                {selectedQuizName}\n              </span>\n            </p>\n          </div>\n          <Button\n            onClick={onClose}\n            variant=\"outline\"\n            className=\"border-slate-600 text-slate-300 hover:text-white\"\n          >\n            ← Back to Quizzes\n          </Button>\n        </div>\n\n        {editingQuestion ? (\n          <QuestionForm\n            selectedQuizId={selectedQuizId}\n            editingQuestion={editingQuestion}\n            onQuestionSaved={handleQuestionSaved}\n            onCancel={handleCancelEdit}\n          />\n        ) : (\n          <QuestionForm\n            selectedQuizId={selectedQuizId}\n            onQuestionSaved={handleQuestionSaved}\n            onCancel={handleCancelEdit}\n          />\n        )}\n\n        <AiQuestionGenerator\n          selectedQuizId={selectedQuizId}\n          selectedQuizName={selectedQuizName}\n          studyDocumentId={studyDocumentId}\n          onGenerationComplete={handleGenerationComplete}\n        />\n\n        <QuestionsList\n          questions={questions}\n          loading={loading}\n          onRefreshQuestions={fetchQuestions}\n          selectedQuizId={selectedQuizId}\n        />\n      </div>\n    </div>\n  );\n};\n", "modifiedCode": "import React, { useEffect, useState, useRef, useCallback } from \"react\";\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables } from \"../../types/supabase\";\nimport { QuestionForm } from \"./QuestionForm\";\nimport { AiQuestionGenerator } from \"./AiQuestionGenerator\";\nimport { QuestionsList, QuestionsListProps } from \"./QuestionsList\";\nimport { Button } from \"@/components/ui/button\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\ninterface QuizQuestionManagerProps {\n  selectedQuizId: string;\n  selectedQuizName: string;\n  studyDocumentId?: string;\n  onClose: () => void;\n}\n\nexport const QuizQuestionManager: React.FC<QuizQuestionManagerProps> = ({\n  selectedQuizId,\n  selectedQuizName,\n  studyDocumentId,\n  onClose,\n}) => {\n  const { user, signOut } = useAuth();\n  const [questions, setQuestions] = useState<QuizQuestion[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(\n    null\n  );\n\n  // Refs to manage polling and prevent memory leaks\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const mountedRef = useRef(true);\n  const retryCountRef = useRef(0);\n  const abortControllerRef = useRef<AbortController | null>(null);\n  const consecutiveFailuresRef = useRef(0);\n  const lastSuccessTimeRef = useRef(Date.now());\n  const maxRetries = 3;\n  const maxConsecutiveFailures = 5; // Circuit breaker threshold\n  const pollingInterval = 15000; // Increased to 15 seconds to reduce load\n\n  // Clear polling interval and abort any ongoing requests\n  const clearPolling = useCallback(() => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n\n  // Handle authentication errors - simplified to avoid dependency cycles\n  const handleAuthError = useCallback(async () => {\n    console.warn('Authentication failed in QuizQuestionManager, signing out...');\n    // Clear polling first\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n\n    try {\n      await signOut();\n    } catch (error) {\n      console.error('Error during signOut:', error);\n    }\n  }, [signOut]); // Removed clearPolling dependency to break cycle\n\n  const fetchQuestions = useCallback(async (isRetry = false) => {\n    if (!user || !selectedQuizId || !mountedRef.current) return;\n\n    // Circuit breaker: Stop making requests if too many consecutive failures\n    if (consecutiveFailuresRef.current >= maxConsecutiveFailures) {\n      const timeSinceLastSuccess = Date.now() - lastSuccessTimeRef.current;\n      const cooldownPeriod = 60000; // 1 minute cooldown\n\n      if (timeSinceLastSuccess < cooldownPeriod) {\n        console.warn(`Circuit breaker active: ${consecutiveFailuresRef.current} consecutive failures. Waiting ${Math.ceil((cooldownPeriod - timeSinceLastSuccess) / 1000)}s before retry.`);\n        return;\n      } else {\n        // Reset circuit breaker after cooldown\n        console.log('Circuit breaker cooldown complete, resetting failure count');\n        consecutiveFailuresRef.current = 0;\n      }\n    }\n\n    // Cancel any previous request\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n    }\n\n    // Create new abort controller for this request\n    abortControllerRef.current = new AbortController();\n    const signal = abortControllerRef.current.signal;\n\n    // Don't show loading on retries to avoid UI flicker\n    if (!isRetry) {\n      setLoading(true);\n    }\n\n    try {\n      const token = localStorage.getItem('auth_token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const response = await fetch(`/api/quizzes/${selectedQuizId}/questions`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n        signal, // Add abort signal to request\n      });\n\n      if (!response.ok) {\n        if (response.status === 401) {\n          // Authentication failed - handle it properly\n          await handleAuthError();\n          return;\n        }\n\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch questions`);\n      }\n\n      const data = await response.json();\n\n      if (mountedRef.current && !signal.aborted) {\n        setQuestions(data || []);\n        setError(null);\n        retryCountRef.current = 0; // Reset retry count on success\n        consecutiveFailuresRef.current = 0; // Reset circuit breaker on success\n        lastSuccessTimeRef.current = Date.now(); // Update last success time\n      }\n    } catch (err: any) {\n      // Don't log errors for aborted requests\n      if (err.name === 'AbortError') {\n        console.log('Request was cancelled');\n        return;\n      }\n\n      console.error('Error fetching questions:', err);\n\n      if (mountedRef.current && !signal.aborted) {\n        // Only set error if we're still mounted and it's not an auth error or network error\n        if (!err.message?.includes('authentication') &&\n            !err.message?.includes('token') &&\n            !err.message?.includes('Failed to fetch')) {\n          setError(err.message || \"Failed to fetch questions.\");\n\n          // Implement exponential backoff for retries (only for non-auth errors)\n          if (retryCountRef.current < maxRetries) {\n            retryCountRef.current++;\n            const delay = Math.pow(2, retryCountRef.current) * 2000; // 4s, 8s, 16s\n            console.log(`Retrying in ${delay}ms (attempt ${retryCountRef.current}/${maxRetries})`);\n\n            setTimeout(() => {\n              if (mountedRef.current) {\n                fetchQuestions(true);\n              }\n            }, delay);\n          }\n        } else if (err.message?.includes('Failed to fetch')) {\n          // Network error - stop polling to prevent spam\n          console.warn('Network error detected, stopping polling to prevent request spam');\n          if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n            intervalRef.current = null;\n          }\n          setError(\"Network connection lost. Please refresh the page to retry.\");\n        }\n      }\n    } finally {\n      if (mountedRef.current && !isRetry && !signal.aborted) {\n        setLoading(false);\n      }\n    }\n  }, [user, selectedQuizId, handleAuthError]);\n\n  // Simplified useEffect to break dependency cycles\n  useEffect(() => {\n    // Mark as mounted\n    mountedRef.current = true;\n\n    // Initial fetch\n    if (user && selectedQuizId) {\n      fetchQuestions();\n\n      // Start polling after a delay to allow initial fetch to complete\n      const startPollingTimer = setTimeout(() => {\n        if (mountedRef.current && user && selectedQuizId) {\n          intervalRef.current = setInterval(() => {\n            if (mountedRef.current && user && selectedQuizId) {\n              fetchQuestions(true); // Mark as retry to avoid loading state\n            }\n          }, pollingInterval);\n        }\n      }, 2000); // 2 second delay before starting polling\n\n      return () => {\n        clearTimeout(startPollingTimer);\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n          intervalRef.current = null;\n        }\n        if (abortControllerRef.current) {\n          abortControllerRef.current.abort();\n          abortControllerRef.current = null;\n        }\n      };\n    }\n  }, [user, selectedQuizId]); // Simplified dependencies - only the essential ones\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      mountedRef.current = false;\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n      if (abortControllerRef.current) {\n        abortControllerRef.current.abort();\n        abortControllerRef.current = null;\n      }\n    };\n  }, []); // Empty dependency array for cleanup only\n\n  const handleQuestionSaved = useCallback(() => {\n    setEditingQuestion(null);\n    fetchQuestions();\n  }, [fetchQuestions]);\n\n  const handleCancelEdit = useCallback(() => {\n    setEditingQuestion(null);\n  }, []);\n\n  const handleGenerationComplete = useCallback(() => {\n    fetchQuestions();\n  }, [fetchQuestions]);\n\n  if (loading)\n    return (\n      <p className=\"text-slate-300\">\n        Loading questions for \"{selectedQuizName}\"...\n      </p>\n    );\n  if (error) return <p className=\"text-red-400\">Error: {error}</p>;\n\n  return (\n    <div className=\"mt-5 space-y-6\">\n      <div className=\"bg-slate-800 shadow-lg rounded-lg border border-slate-700 p-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <div>\n            <h4 className=\"text-2xl font-bold text-slate-200 mb-2\">\n              Manage Questions\n            </h4>\n            <p className=\"text-slate-400 text-sm\">\n              Quiz:{\" \"}\n              <span className=\"text-purple-400 font-medium\">\n                {selectedQuizName}\n              </span>\n            </p>\n          </div>\n          <Button\n            onClick={onClose}\n            variant=\"outline\"\n            className=\"border-slate-600 text-slate-300 hover:text-white\"\n          >\n            ← Back to Quizzes\n          </Button>\n        </div>\n\n        {editingQuestion ? (\n          <QuestionForm\n            selectedQuizId={selectedQuizId}\n            editingQuestion={editingQuestion}\n            onQuestionSaved={handleQuestionSaved}\n            onCancel={handleCancelEdit}\n          />\n        ) : (\n          <QuestionForm\n            selectedQuizId={selectedQuizId}\n            onQuestionSaved={handleQuestionSaved}\n            onCancel={handleCancelEdit}\n          />\n        )}\n\n        <AiQuestionGenerator\n          selectedQuizId={selectedQuizId}\n          selectedQuizName={selectedQuizName}\n          studyDocumentId={studyDocumentId}\n          onGenerationComplete={handleGenerationComplete}\n        />\n\n        <QuestionsList\n          questions={questions}\n          loading={loading}\n          onRefreshQuestions={fetchQuestions}\n          selectedQuizId={selectedQuizId}\n        />\n      </div>\n    </div>\n  );\n};\n"}