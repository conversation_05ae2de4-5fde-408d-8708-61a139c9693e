{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/FlashcardsPage.tsx"}, "originalCode": "import React, { useState, Change<PERSON><PERSON>, useEffect } from \"react\";\nimport { <PERSON> } from \"wouter\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n} from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\n\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\nimport { Terminal, FilePenLine, Trash2 } from \"lucide-react\";\nimport AppLayout from \"@/components/layout/AppLayout\";\nimport {\n  FlashcardSet as SharedFlashcardDeck,\n  Flashcard as SharedFlashcard,\n} from \"@shared/types/flashcards\";\nimport { FlashcardDeck, Flashcard } from \"@/types\";\nimport { generateFlashcardsAPI } from \"@/lib/api\";\nimport {\n  getAIProviderSettings,\n  isAIProviderConfigured,\n} from \"@/lib/ai-provider\";\nimport { extractTextFromFile } from \"@/lib/file-parser\";\n// FlashcardSetList import removed\nimport { QuizGenerationPopup } from \"../components/flashcards/QuizGenerationPopup\";\nimport { useLocation } from \"wouter\";\nimport { useToast } from \"@/hooks/use-toast\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { Tables } from \"@/types/supabase\";\nimport Spinner from \"@/components/ui/Spinner\";\n\n// Get the backend API URL from environment variables\nconst API_BASE_URL = import.meta.env.VITE_API_BASE_URL || \"http://localhost:5000/api\";\n\ninterface QuizGenerationOptions {\n  numberOfQuestions: number;\n  questionTypes: string[];\n  customPrompt: string;\n}\n\nconst FlashcardsPage: React.FC = () => {\n  const { user } = useAuth();\n\n  // File upload states\n  const [uploadingFile, setUploadingFile] = useState<boolean>(false);\n\n  // Document selection states\n  const [availableDocuments, setAvailableDocuments] = useState<\n    Tables<\"study_documents\">[]\n  >([]);\n  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);\n  const [loadingDocuments, setLoadingDocuments] = useState<boolean>(false);\n  const [customPrompt, setCustomPrompt] = useState<string>(\"\");\n\n  // Common states\n  const [deckTitle, setDeckTitle] = useState<string>(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [generatedDeck, setGeneratedDeck] = useState<FlashcardDeck | null>(\n    null\n  );\n  const [userDecks, setUserDecks] = useState<FlashcardDeck[]>([]);\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [generatingQuizForDeck, setGeneratingQuizForDeck] = useState<\n    string | null\n  >(null);\n  const [isAIConfigured, setIsAIConfigured] = useState<boolean>(false);\n\n  // Check AI configuration on mount\n  useEffect(() => {\n    const checkAIConfig = async () => {\n      const configured = await isAIProviderConfigured();\n      setIsAIConfigured(configured);\n    };\n    checkAIConfig();\n  }, []);\n\n  // Load existing decks on mount\n  useEffect(() => {\n    const fetchDecks = async () => {\n      if (!user) return;\n\n      try {\n        const token = localStorage.getItem('auth_token');\n\n        if (!token) {\n          console.warn(\"No auth token found when loading decks\");\n          return;\n        }\n\n        const response = await fetch(`${API_BASE_URL}/flashcard-sets`, {\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (!response.ok) {\n          console.error(\"Failed to fetch flashcard sets:\", response.statusText);\n          return;\n        }\n\n        const flashcardSets = await response.json();\n\n        // Convert Supabase flashcard sets to client format\n        const decks: FlashcardDeck[] = flashcardSets.map((set: any) => ({\n          id: set.id,\n          name: set.name,\n          description: set.description || \"\",\n          documentId: set.study_document_id || \"\",\n          createdAt: set.created_at ? new Date(set.created_at).getTime() : Date.now(),\n          totalCards: set.card_count || 0, // Use the card count from backend\n          dueTodayCount: set.card_count || 0, // For now, assume all cards are due\n          masteredCount: 0,\n        }));\n\n        setUserDecks(decks);\n      } catch (error) {\n        console.error(\"Error fetching flashcard sets:\", error);\n      }\n    };\n\n    fetchDecks();\n  }, [user]);\n\n  // Load documents on mount\n  useEffect(() => {\n    const fetchDocuments = async () => {\n      if (user) {\n        setLoadingDocuments(true);\n        try {\n          const token = localStorage.getItem('auth_token');\n          if (!token) {\n            throw new Error('No authentication token found');\n          }\n\n          const response = await fetch(`${API_BASE_URL}/documents`, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${token}`,\n            },\n          });\n\n          if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n          }\n\n          const data = await response.json();\n          // Filter for extracted documents only\n          const extractedDocs = data.filter((doc: any) => doc.status === 'extracted');\n          setAvailableDocuments(extractedDocs || []);\n        } catch (err: any) {\n          console.error(\n            \"Error fetching documents for flashcard generation:\",\n            err\n          );\n          toast({\n            title: \"Error\",\n            description: \"Could not load documents for AI generation.\",\n            variant: \"destructive\",\n          });\n        } finally {\n          setLoadingDocuments(false);\n        }\n      }\n    };\n    fetchDocuments();\n  }, [user, toast]);\n\n  // Document selection handler\n  const handleDocumentSelection = (documentId: string) => {\n    setSelectedDocumentIds((prev) =>\n      prev.includes(documentId)\n        ? prev.filter((id) => id !== documentId)\n        : [...prev, documentId]\n    );\n  };\n\n  const handleFileUpload = async (event: ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file || !user) return;\n\n    setUploadingFile(true);\n    setError(null);\n\n    try {\n      // Extract text from file\n      const doc = await extractTextFromFile(file);\n      if (doc.content.trim().length === 0) {\n        throw new Error(\n          \"Extracted text is empty. The document might be image-based or corrupted.\"\n        );\n      }\n\n      // Upload to server\n      const token = localStorage.getItem('auth_token');\n      if (!token) {\n        throw new Error(\n          \"No authentication session found. Please log in again.\"\n        );\n      }\n\n      const response = await fetch(`${API_BASE_URL}/documents`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${token}`,\n        },\n        body: JSON.stringify({\n          fileName: file.name,\n          content: doc.content,\n          contentType: file.type,\n          sizeBytes: file.size,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`\n        );\n      }\n\n      const uploadedDoc = await response.json();\n\n      // Add to available documents and auto-select it\n      setAvailableDocuments((prev) => [uploadedDoc, ...prev]);\n      setSelectedDocumentIds((prev) => [...prev, uploadedDoc.id]);\n\n      // Set deck title from filename if not already set\n      if (!deckTitle.trim()) {\n        setDeckTitle(file.name.split(\".\").slice(0, -1).join(\".\"));\n      }\n\n      toast({\n        title: \"Document Uploaded\",\n        description: `\"${file.name}\" has been uploaded and selected for flashcard generation.`,\n      });\n\n      // Clear file input\n      event.target.value = \"\";\n    } catch (e) {\n      console.error(\"Error uploading file:\", e);\n      setError(\n        e instanceof Error ? e.message : \"Failed to upload and process file.\"\n      );\n    } finally {\n      setUploadingFile(false);\n    }\n  };\n\n  const mapSharedDeckToClient = (\n    sharedDeck: any\n  ): { deck: FlashcardDeck; cards: Flashcard[] } => {\n    console.log(\"🔍 Mapping shared deck to client:\", sharedDeck);\n\n    if (!sharedDeck) {\n      throw new Error(\"Received undefined or null deck from server\");\n    }\n\n    // Handle the new response format with 'set' and 'flashcards' properties\n    let setData = sharedDeck.set || sharedDeck;\n    let flashcardsData = sharedDeck.flashcards || [];\n\n    // Handle legacy format with 'deck' property\n    if (\"deck\" in sharedDeck && sharedDeck.deck) {\n      setData = sharedDeck.deck;\n      flashcardsData = setData.flashcards || [];\n    }\n\n    const deck: FlashcardDeck = {\n      id: setData.id || crypto.randomUUID(),\n      name: setData.name || setData.title || \"Untitled Deck\",\n      description: setData.description || `Generated from document ${\n        setData.study_document_id || setData.documentId || \"unknown\"\n      }`,\n      documentId: setData.study_document_id || setData.documentId || \"\",\n      createdAt: setData.created_at\n        ? new Date(setData.created_at).getTime()\n        : setData.createdAt || Date.now(),\n      totalCards: 0, // Will update after processing flashcards\n      dueTodayCount: 0,\n      masteredCount: 0,\n    };\n\n    const cards: Flashcard[] = [];\n\n    // Process flashcards if available\n    if (Array.isArray(flashcardsData) && flashcardsData.length > 0) {\n      cards.push(\n        ...flashcardsData.map((fc: any) => ({\n          id: fc.id || crypto.randomUUID(),\n          question: fc.front_text || fc.question || \"No question\",\n          answer: fc.back_text || fc.answer || \"No answer\",\n          deckId: fc.set_id || fc.deckId || deck.id,\n          createdAt: fc.created_at\n            ? new Date(fc.created_at).getTime()\n            : Date.now(),\n        }))\n      );\n\n      // Update totalCards with actual count\n      deck.totalCards = cards.length;\n      deck.dueTodayCount = cards.length;\n    }\n\n    console.log(\"✅ Mapped deck:\", { deck, cardsCount: cards.length });\n    return { deck, cards };\n  };\n\n  // New AI-powered flashcard generation from multiple documents\n  const handleAiFlashcardGeneration = async () => {\n    if (!isAIConfigured) {\n      setError(\"AI Provider not configured. Please configure your AI settings first.\");\n      return;\n    }\n\n    if (selectedDocumentIds.length === 0) {\n      setError(\"Please select at least one document.\");\n      return;\n    }\n\n    if (!deckTitle.trim()) {\n      setError(\"Please provide a deck title.\");\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const aiSettings = getAIProviderSettings();\n      const token = localStorage.getItem('auth_token');\n\n      if (!token) {\n        throw new Error(\n          \"No authentication session found. Please log in again.\"\n        );\n      }\n\n      // Fetch document contents using the API\n      const documentContents: string[] = [];\n      for (const docId of selectedDocumentIds) {\n        try {\n          const response = await fetch(`${API_BASE_URL}/documents/${docId}/content`, {\n            method: \"GET\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n              Authorization: `Bearer ${token}`,\n            },\n          });\n\n          if (!response.ok) {\n            throw new Error(\n              `Failed to fetch document ${docId}: HTTP ${response.status}`\n            );\n          }\n\n          const textContent = await response.text();\n          if (textContent.trim()) {\n            documentContents.push(textContent);\n          }\n        } catch (err) {\n          console.error(`Error fetching document ${docId}:`, err);\n          throw new Error(\n            `Failed to fetch document ${docId}: ${\n              err instanceof Error ? err.message : \"Unknown error\"\n            }`\n          );\n        }\n      }\n\n      if (documentContents.length === 0) {\n        throw new Error(\"No valid document content found.\");\n      }\n\n      // Combine all document contents\n      const combinedContent = documentContents.join(\n        \"\\n\\n--- Document Separator ---\\n\\n\"\n      );\n\n      // Enhanced API payload with custom prompt support\n      const enhancedPayload = {\n        textContent: combinedContent,\n        documentId: selectedDocumentIds.join(\",\"), // Multiple document IDs\n        deckTitle: deckTitle,\n        customPrompt: customPrompt || undefined,\n        // aiSettings removed - credentials are retrieved from secure backend storage\n      };\n\n      console.log(\"📤 Sending flashcard generation request:\", enhancedPayload);\n      const sharedDeck = await generateFlashcardsAPI(enhancedPayload);\n      console.log(\"📥 Received flashcard generation response:\", sharedDeck);\n\n      if (!sharedDeck) {\n        throw new Error(\"No response received from flashcard generation API\");\n      }\n\n      const { deck, cards } = mapSharedDeckToClient(sharedDeck);\n\n      // Save flashcard set via Express.js backend\n      const authToken = localStorage.getItem('auth_token');\n\n      if (!authToken) {\n        throw new Error(\"No authentication session found. Please log in again.\");\n      }\n\n      const createSetPayload = {\n        name: deck.name,\n        description: deck.description || `Generated from ${selectedDocumentIds.length} document(s)`,\n        study_document_id: selectedDocumentIds[0] || null, // Use first document ID\n        flashcards: cards.map((card) => ({\n          front_text: card.question,\n          back_text: card.answer,\n        })),\n      };\n\n      const response = await fetch(`${API_BASE_URL}/flashcard-sets`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${authToken}`,\n        },\n        body: JSON.stringify(createSetPayload),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`\n        );\n      }\n\n      const savedFlashcardSet = await response.json();\n\n      // Update deck with the correct backend ID and card count\n      const updatedDeck = {\n        ...deck,\n        id: savedFlashcardSet.id,\n        name: savedFlashcardSet.name,\n        totalCards: cards.length,\n        dueTodayCount: cards.length,\n      };\n\n      setGeneratedDeck(updatedDeck);\n      setUserDecks((prev) => [updatedDeck, ...prev]);\n\n      // Clear selections\n      setSelectedDocumentIds([]);\n      setCustomPrompt(\"\");\n      setDeckTitle(\"\");\n\n      toast({\n        title: \"Success!\",\n        description: `Generated \"${deck.name}\" with ${cards.length} flashcards from ${selectedDocumentIds.length} document(s).`,\n      });\n    } catch (e) {\n      console.error(\"Error generating flashcards from documents:\", e);\n      setError(\n        e instanceof Error\n          ? e.message\n          : \"Failed to generate flashcards from documents.\"\n      );\n    }\n    setIsLoading(false);\n  };\n\n  const handleManageDeck = (deckId: string) => {\n    navigate(`/flashcards/edit/${deckId}`);\n  };\n\n  const handleDeleteDeck = async (deckId: string, deckName: string) => {\n    if (\n      !window.confirm(\n        `Are you sure you want to delete the deck \"${deckName}\"? All flashcards in this deck will also be deleted.`\n      )\n    )\n      return;\n\n    try {\n      const token = localStorage.getItem('auth_token');\n\n      if (!token) {\n        throw new Error(\"No authentication session found. Please log in again.\");\n      }\n\n      const response = await fetch(`${API_BASE_URL}/flashcard-sets/${deckId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`\n        );\n      }\n\n      // Remove from local state\n      setUserDecks((prev) => prev.filter((deck) => deck.id !== deckId));\n\n      toast({\n        title: \"Success\",\n        description: `Deck \"${deckName}\" has been deleted.`,\n      });\n    } catch (error) {\n      console.error(\"Error deleting deck:\", error);\n      toast({\n        title: \"Error\",\n        description: error instanceof Error ? error.message : \"Failed to delete the deck. Please try again.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n  const handleGenerateQuizFromDeck = async (\n    deck: FlashcardDeck,\n    options: QuizGenerationOptions\n  ) => {\n    if (!isAIConfigured) {\n      toast({\n        title: \"AI Provider Not Configured\",\n        description:\n          \"Please configure your AI provider settings to generate quizzes.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    if (generatingQuizForDeck) {\n      return; // Prevent multiple simultaneous generations\n    }\n\n    setGeneratingQuizForDeck(deck.id);\n\n    try {\n      // Fetch flashcards from backend API instead of local storage\n      const token = localStorage.getItem('auth_token');\n\n      if (!token) {\n        throw new Error(\n          \"No authentication session found. Please log in again.\"\n        );\n      }\n\n      console.log(\"🔍 Fetching flashcards for deck:\", {\n        deckId: deck.id,\n        deckName: deck.name,\n        totalCards: deck.totalCards\n      });\n\n      const response = await fetch(`${API_BASE_URL}/flashcard-sets/${deck.id}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(\n          errorData.error || `Failed to fetch flashcards: ${response.statusText}`\n        );\n      }\n\n      const flashcardSetData = await response.json();\n      const flashcards = flashcardSetData.flashcards || [];\n\n      console.log(\"📥 Fetched flashcards:\", {\n        deckId: deck.id,\n        flashcardCount: flashcards.length,\n        flashcardSetData: flashcardSetData\n      });\n\n      if (flashcards.length === 0) {\n        toast({\n          title: \"No Flashcards Found\",\n          description: \"This deck has no flashcards to generate a quiz from.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Map flashcards from backend format to expected format\n      const textContent = flashcards\n        .map((card: any) => `Question: ${card.front_text || card.question}\\nAnswer: ${card.back_text || card.answer}`)\n        .join(\"\\n\\n\");\n\n      const aiSettings = getAIProviderSettings();\n      const quizTitle = `Quiz: ${deck.name}`;\n      const numberOfQuestions = Math.min(\n        flashcards.length,\n        options.numberOfQuestions\n      );\n\n      console.log(\"🎯 Quiz Generation Debug Info:\", {\n        deckId: deck.id,\n        deckName: deck.name,\n        flashcardCount: flashcards.length,\n        textContentLength: textContent.length,\n        quizTitle,\n        numberOfQuestions,\n        aiProvider: aiSettings.provider,\n        aiModel: aiSettings.generationModel,\n        hasApiKey: !!aiSettings.apiKey,\n        sampleFlashcard: flashcards[0] // Log first flashcard for debugging\n      });\n      const requestBody = {\n        textContent,\n        quizName: quizTitle,\n        generationOptions: {\n          numberOfQuestions,\n          questionTypes: options.questionTypes,\n          customPrompt: options.customPrompt,\n        },\n        // AI config is now handled by the backend using stored credentials\n      };\n\n      console.log(\"📤 Sending quiz generation request:\", {\n        url: `${API_BASE_URL}/quizzes/generate`,\n        method: \"POST\",\n        hasAuth: !!token,\n        bodyKeys: Object.keys(requestBody),\n      });\n\n      const generateQuizResponse = await fetch(`${API_BASE_URL}/quizzes/generate`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${token}`,\n        },\n        body: JSON.stringify(requestBody),\n      });\n\n      console.log(\"📥 Quiz generation response:\", {\n        status: quizResponse.status,\n        statusText: quizResponse.statusText,\n        ok: quizResponse.ok,\n      });\n\n      if (!quizResponse.ok) {\n        const errorData = await quizResponse.json().catch(() => ({}));\n        console.error(\"❌ Quiz generation error details:\", errorData);\n        throw new Error(\n          errorData.error ||\n            errorData.message ||\n            `HTTP ${quizResponse.status}: ${quizResponse.statusText}`\n        );\n      }\n\n      const quizData = await quizResponse.json();\n      console.log(\"✅ Quiz generated successfully:\", {\n        quizId: quizData.quizId || quizData.id,\n        hasQuiz: !!quizData.quiz,\n      });\n\n      const finalQuizId = quizData.quizId || quizData.id;\n      if (!finalQuizId) {\n        throw new Error(\"No quiz ID returned from server\");\n      }\n      toast({\n        title: \"Quiz Generated Successfully\",\n        description: `Created \"${quizTitle}\" with ${numberOfQuestions} questions!`,\n      });\n\n      // Navigate to quizzes page and show generating notification\n      navigate(\"/quizzes\");\n\n      // Show a temporary notification that the quiz is being added to the list\n      setTimeout(() => {\n        toast({\n          title: \"Quiz Ready\",\n          description: `\"${quizTitle}\" has been added to your quizzes!`,\n        });\n      }, 1000);\n    } catch (error: any) {\n      console.error(\"❌ Error generating quiz:\", error);\n      console.error(\"Error stack:\", error.stack);\n      toast({\n        title: \"Quiz Generation Failed\",\n        description:\n          error.message || \"Failed to generate quiz from flashcards.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setGeneratingQuizForDeck(null);\n    }\n  };\n\n  return (\n    <AppLayout title=\"Flashcards\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h1 className=\"text-3xl font-bold text-slate-800 dark:text-slate-100\">\n            Flashcard Sets\n          </h1>\n        </div>\n\n        {/* Create New Deck Card */}\n        <Card className=\"mb-8 bg-purple-900 bg-opacity-20 border-purple-700\">\n          <CardHeader>\n            <CardTitle className=\"text-slate-100\">Create New Deck</CardTitle>\n            <CardDescription className=\"text-purple-300\">\n              Select documents and customize AI generation settings, or upload a\n              new document.\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-6\">\n              {/* Deck Title */}\n              <div>\n                <Label htmlFor=\"deck-title\" className=\"text-slate-300\">\n                  Deck Title*\n                </Label>\n                <Input\n                  id=\"deck-title\"\n                  type=\"text\"\n                  value={deckTitle}\n                  onChange={(e) => setDeckTitle(e.target.value)}\n                  placeholder=\"e.g., Chapter 1 Flashcards\"\n                  disabled={isLoading || uploadingFile}\n                  className=\"mt-1 !bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500 focus:ring-purple-500 focus:border-purple-500\"\n                />\n              </div>\n\n              {/* File Upload Section */}\n              <div>\n                <Label htmlFor=\"file-upload\" className=\"text-slate-300\">\n                  Upload New Document (Optional)\n                </Label>\n                <Input\n                  id=\"file-upload\"\n                  type=\"file\"\n                  accept=\".pdf,.docx,.txt,.md\"\n                  onChange={handleFileUpload}\n                  disabled={isLoading || uploadingFile}\n                  className=\"mt-1 !bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500 file:border-0 file:bg-transparent file:text-purple-400\"\n                />\n                <p className=\"text-xs text-slate-400 mt-1\">\n                  {uploadingFile\n                    ? \"Uploading and processing...\"\n                    : \"Upload a PDF, DOCX, TXT, or MD file to add to your documents\"}\n                </p>\n              </div>\n\n              {/* Document Selection */}\n              <div>\n                <Label className=\"text-slate-300 mb-2 block\">\n                  Select Documents for AI Generation*\n                </Label>\n                {loadingDocuments ? (\n                  <div className=\"flex justify-center items-center py-4\">\n                    <Spinner size=\"sm\" />\n                    <span className=\"ml-2 text-purple-300\">\n                      Loading documents...\n                    </span>\n                  </div>\n                ) : availableDocuments.length === 0 ? (\n                  <p className=\"text-sm text-purple-300 p-3 bg-slate-700 rounded-md\">\n                    No extracted documents available. Please upload and process\n                    documents first.\n                  </p>\n                ) : (\n                  <div className=\"max-h-60 overflow-y-auto space-y-2 p-3 bg-slate-700/50 rounded-md border border-slate-600\">\n                    {availableDocuments.map((doc) => (\n                      <div\n                        key={doc.id}\n                        className=\"flex items-center space-x-2 p-2 bg-slate-700 rounded hover:bg-slate-600/70 transition-colors\"\n                      >\n                        <Checkbox\n                          id={`ai-doc-${doc.id}`}\n                          checked={selectedDocumentIds.includes(doc.id)}\n                          onCheckedChange={() =>\n                            handleDocumentSelection(doc.id)\n                          }\n                          disabled={isLoading}\n                        />\n                        <Label\n                          htmlFor={`ai-doc-${doc.id}`}\n                          className=\"font-normal text-purple-300 cursor-pointer flex-1 truncate\"\n                          title={doc.file_name}\n                        >\n                          {doc.file_name}\n                        </Label>\n                      </div>\n                    ))}\n                  </div>\n                )}\n                {selectedDocumentIds.length > 0 && (\n                  <p className=\"text-xs text-purple-400 mt-1\">\n                    {selectedDocumentIds.length} document(s) selected.\n                  </p>\n                )}\n              </div>\n\n              {/* Custom Prompt */}\n              <div>\n                <Label htmlFor=\"custom-prompt\" className=\"text-slate-300\">\n                  Custom Prompt (Optional)\n                </Label>\n                <Textarea\n                  id=\"custom-prompt\"\n                  value={customPrompt}\n                  onChange={(e) => setCustomPrompt(e.target.value)}\n                  placeholder=\"e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'\"\n                  rows={3}\n                  className=\"mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\"\n                  disabled={isLoading}\n                />\n                <p className=\"text-xs text-purple-400 mt-1\">\n                  Add specific instructions for the AI on what kind of\n                  flashcards you want.\n                </p>\n              </div>\n\n              {error && (\n                <Alert variant=\"destructive\">\n                  <Terminal className=\"h-4 w-4\" />\n                  <AlertTitle>Error</AlertTitle>\n                  <AlertDescription>{error}</AlertDescription>\n                </Alert>\n              )}\n\n              <Button\n                onClick={handleAiFlashcardGeneration}\n                disabled={\n                  isLoading ||\n                  uploadingFile ||\n                  selectedDocumentIds.length === 0 ||\n                  !deckTitle.trim() ||\n                  !isAIConfigured\n                }\n                className=\"w-full\"\n              >\n                {isLoading\n                  ? \"Generating Flashcards...\"\n                  : \"Generate Flashcards with AI\"}\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n        {generatedDeck && (\n          <Card className=\"mb-8 bg-green-900 bg-opacity-20 border-green-700\">\n            <CardHeader>\n              <CardTitle className=\"text-green-300\">Deck Generated!</CardTitle>\n              <CardDescription className=\"text-green-400\">\n                {generatedDeck.totalCards} cards ready.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Link href={`/flashcards/${generatedDeck.id}`}>\n                <Button>Review Deck</Button>\n              </Link>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Existing Decks */}\n        <h2 className=\"text-2xl font-semibold mb-4 text-slate-100\">My Decks</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {userDecks.map((deck) => (\n            <Card\n              key={deck.id}\n              className=\"bg-purple-800 bg-opacity-20 border-purple-700\"\n            >\n              <CardHeader>\n                <CardTitle className=\"text-slate-200\">{deck.name}</CardTitle>\n              </CardHeader>\n              <CardContent className=\"flex flex-col space-y-4\">\n                <p className=\"text-slate-400\">{deck.totalCards} Cards</p>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  {\" \"}\n                  {/* 2x2 grid with consistent spacing */}\n                  {/* Row 1: Review & Generate Quiz */}\n                  <Link href={`/flashcards/${deck.id}`} className=\"w-full\">\n                    <Button className=\"w-full h-10 bg-purple-600 hover:bg-purple-700 text-white cursor-pointer\">\n                      Review\n                    </Button>\n                  </Link>\n                  <QuizGenerationPopup\n                    trigger={\n                      <Button\n                        disabled={generatingQuizForDeck === deck.id}\n                        className=\"w-full h-10 bg-purple-600 hover:bg-purple-700 text-white cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        {generatingQuizForDeck === deck.id\n                          ? \"Generating...\"\n                          : \"Generate Quiz\"}\n                      </Button>\n                    }\n                    onGenerate={(options) =>\n                      handleGenerateQuizFromDeck(deck, options)\n                    }\n                    isGenerating={generatingQuizForDeck === deck.id}\n                    disabled={generatingQuizForDeck === deck.id}\n                    maxQuestions={deck.totalCards || 20}\n                  />\n                  {/* Row 2: Manage & Delete */}\n                  <Button\n                    onClick={() => handleManageDeck(deck.id)}\n                    className=\"w-full h-10 bg-purple-600 hover:bg-purple-700 text-white cursor-pointer\"\n                  >\n                    <FilePenLine size={16} className=\"mr-1\" /> Manage\n                  </Button>\n                  <Button\n                    variant=\"destructive\"\n                    onClick={() => handleDeleteDeck(deck.id, deck.name)}\n                    className=\"w-full h-10 cursor-pointer\"\n                  >\n                    <Trash2 size={16} className=\"mr-1\" /> Delete\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n          {userDecks.length === 0 && (\n            <p className=\"text-slate-400\">No decks yet. Generate one above!</p>\n          )}\n        </div>\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default FlashcardsPage;\n", "modifiedCode": "import React, { useState, Change<PERSON><PERSON>, useEffect } from \"react\";\nimport { <PERSON> } from \"wouter\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n} from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\n\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\nimport { Terminal, FilePenLine, Trash2 } from \"lucide-react\";\nimport AppLayout from \"@/components/layout/AppLayout\";\nimport {\n  FlashcardSet as SharedFlashcardDeck,\n  Flashcard as SharedFlashcard,\n} from \"@shared/types/flashcards\";\nimport { FlashcardDeck, Flashcard } from \"@/types\";\nimport { generateFlashcardsAPI } from \"@/lib/api\";\nimport {\n  getAIProviderSettings,\n  isAIProviderConfigured,\n} from \"@/lib/ai-provider\";\nimport { extractTextFromFile } from \"@/lib/file-parser\";\n// FlashcardSetList import removed\nimport { QuizGenerationPopup } from \"../components/flashcards/QuizGenerationPopup\";\nimport { useLocation } from \"wouter\";\nimport { useToast } from \"@/hooks/use-toast\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { Tables } from \"@/types/supabase\";\nimport Spinner from \"@/components/ui/Spinner\";\n\n// Get the backend API URL from environment variables\nconst API_BASE_URL = import.meta.env.VITE_API_BASE_URL || \"http://localhost:5000/api\";\n\ninterface QuizGenerationOptions {\n  numberOfQuestions: number;\n  questionTypes: string[];\n  customPrompt: string;\n}\n\nconst FlashcardsPage: React.FC = () => {\n  const { user } = useAuth();\n\n  // File upload states\n  const [uploadingFile, setUploadingFile] = useState<boolean>(false);\n\n  // Document selection states\n  const [availableDocuments, setAvailableDocuments] = useState<\n    Tables<\"study_documents\">[]\n  >([]);\n  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);\n  const [loadingDocuments, setLoadingDocuments] = useState<boolean>(false);\n  const [customPrompt, setCustomPrompt] = useState<string>(\"\");\n\n  // Common states\n  const [deckTitle, setDeckTitle] = useState<string>(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [generatedDeck, setGeneratedDeck] = useState<FlashcardDeck | null>(\n    null\n  );\n  const [userDecks, setUserDecks] = useState<FlashcardDeck[]>([]);\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [generatingQuizForDeck, setGeneratingQuizForDeck] = useState<\n    string | null\n  >(null);\n  const [isAIConfigured, setIsAIConfigured] = useState<boolean>(false);\n\n  // Check AI configuration on mount\n  useEffect(() => {\n    const checkAIConfig = async () => {\n      const configured = await isAIProviderConfigured();\n      setIsAIConfigured(configured);\n    };\n    checkAIConfig();\n  }, []);\n\n  // Load existing decks on mount\n  useEffect(() => {\n    const fetchDecks = async () => {\n      if (!user) return;\n\n      try {\n        const token = localStorage.getItem('auth_token');\n\n        if (!token) {\n          console.warn(\"No auth token found when loading decks\");\n          return;\n        }\n\n        const response = await fetch(`${API_BASE_URL}/flashcard-sets`, {\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (!response.ok) {\n          console.error(\"Failed to fetch flashcard sets:\", response.statusText);\n          return;\n        }\n\n        const flashcardSets = await response.json();\n\n        // Convert Supabase flashcard sets to client format\n        const decks: FlashcardDeck[] = flashcardSets.map((set: any) => ({\n          id: set.id,\n          name: set.name,\n          description: set.description || \"\",\n          documentId: set.study_document_id || \"\",\n          createdAt: set.created_at ? new Date(set.created_at).getTime() : Date.now(),\n          totalCards: set.card_count || 0, // Use the card count from backend\n          dueTodayCount: set.card_count || 0, // For now, assume all cards are due\n          masteredCount: 0,\n        }));\n\n        setUserDecks(decks);\n      } catch (error) {\n        console.error(\"Error fetching flashcard sets:\", error);\n      }\n    };\n\n    fetchDecks();\n  }, [user]);\n\n  // Load documents on mount\n  useEffect(() => {\n    const fetchDocuments = async () => {\n      if (user) {\n        setLoadingDocuments(true);\n        try {\n          const token = localStorage.getItem('auth_token');\n          if (!token) {\n            throw new Error('No authentication token found');\n          }\n\n          const response = await fetch(`${API_BASE_URL}/documents`, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${token}`,\n            },\n          });\n\n          if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n          }\n\n          const data = await response.json();\n          // Filter for extracted documents only\n          const extractedDocs = data.filter((doc: any) => doc.status === 'extracted');\n          setAvailableDocuments(extractedDocs || []);\n        } catch (err: any) {\n          console.error(\n            \"Error fetching documents for flashcard generation:\",\n            err\n          );\n          toast({\n            title: \"Error\",\n            description: \"Could not load documents for AI generation.\",\n            variant: \"destructive\",\n          });\n        } finally {\n          setLoadingDocuments(false);\n        }\n      }\n    };\n    fetchDocuments();\n  }, [user, toast]);\n\n  // Document selection handler\n  const handleDocumentSelection = (documentId: string) => {\n    setSelectedDocumentIds((prev) =>\n      prev.includes(documentId)\n        ? prev.filter((id) => id !== documentId)\n        : [...prev, documentId]\n    );\n  };\n\n  const handleFileUpload = async (event: ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file || !user) return;\n\n    setUploadingFile(true);\n    setError(null);\n\n    try {\n      // Extract text from file\n      const doc = await extractTextFromFile(file);\n      if (doc.content.trim().length === 0) {\n        throw new Error(\n          \"Extracted text is empty. The document might be image-based or corrupted.\"\n        );\n      }\n\n      // Upload to server\n      const token = localStorage.getItem('auth_token');\n      if (!token) {\n        throw new Error(\n          \"No authentication session found. Please log in again.\"\n        );\n      }\n\n      const response = await fetch(`${API_BASE_URL}/documents`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${token}`,\n        },\n        body: JSON.stringify({\n          fileName: file.name,\n          content: doc.content,\n          contentType: file.type,\n          sizeBytes: file.size,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`\n        );\n      }\n\n      const uploadedDoc = await response.json();\n\n      // Add to available documents and auto-select it\n      setAvailableDocuments((prev) => [uploadedDoc, ...prev]);\n      setSelectedDocumentIds((prev) => [...prev, uploadedDoc.id]);\n\n      // Set deck title from filename if not already set\n      if (!deckTitle.trim()) {\n        setDeckTitle(file.name.split(\".\").slice(0, -1).join(\".\"));\n      }\n\n      toast({\n        title: \"Document Uploaded\",\n        description: `\"${file.name}\" has been uploaded and selected for flashcard generation.`,\n      });\n\n      // Clear file input\n      event.target.value = \"\";\n    } catch (e) {\n      console.error(\"Error uploading file:\", e);\n      setError(\n        e instanceof Error ? e.message : \"Failed to upload and process file.\"\n      );\n    } finally {\n      setUploadingFile(false);\n    }\n  };\n\n  const mapSharedDeckToClient = (\n    sharedDeck: any\n  ): { deck: FlashcardDeck; cards: Flashcard[] } => {\n    console.log(\"🔍 Mapping shared deck to client:\", sharedDeck);\n\n    if (!sharedDeck) {\n      throw new Error(\"Received undefined or null deck from server\");\n    }\n\n    // Handle the new response format with 'set' and 'flashcards' properties\n    let setData = sharedDeck.set || sharedDeck;\n    let flashcardsData = sharedDeck.flashcards || [];\n\n    // Handle legacy format with 'deck' property\n    if (\"deck\" in sharedDeck && sharedDeck.deck) {\n      setData = sharedDeck.deck;\n      flashcardsData = setData.flashcards || [];\n    }\n\n    const deck: FlashcardDeck = {\n      id: setData.id || crypto.randomUUID(),\n      name: setData.name || setData.title || \"Untitled Deck\",\n      description: setData.description || `Generated from document ${\n        setData.study_document_id || setData.documentId || \"unknown\"\n      }`,\n      documentId: setData.study_document_id || setData.documentId || \"\",\n      createdAt: setData.created_at\n        ? new Date(setData.created_at).getTime()\n        : setData.createdAt || Date.now(),\n      totalCards: 0, // Will update after processing flashcards\n      dueTodayCount: 0,\n      masteredCount: 0,\n    };\n\n    const cards: Flashcard[] = [];\n\n    // Process flashcards if available\n    if (Array.isArray(flashcardsData) && flashcardsData.length > 0) {\n      cards.push(\n        ...flashcardsData.map((fc: any) => ({\n          id: fc.id || crypto.randomUUID(),\n          question: fc.front_text || fc.question || \"No question\",\n          answer: fc.back_text || fc.answer || \"No answer\",\n          deckId: fc.set_id || fc.deckId || deck.id,\n          createdAt: fc.created_at\n            ? new Date(fc.created_at).getTime()\n            : Date.now(),\n        }))\n      );\n\n      // Update totalCards with actual count\n      deck.totalCards = cards.length;\n      deck.dueTodayCount = cards.length;\n    }\n\n    console.log(\"✅ Mapped deck:\", { deck, cardsCount: cards.length });\n    return { deck, cards };\n  };\n\n  // New AI-powered flashcard generation from multiple documents\n  const handleAiFlashcardGeneration = async () => {\n    if (!isAIConfigured) {\n      setError(\"AI Provider not configured. Please configure your AI settings first.\");\n      return;\n    }\n\n    if (selectedDocumentIds.length === 0) {\n      setError(\"Please select at least one document.\");\n      return;\n    }\n\n    if (!deckTitle.trim()) {\n      setError(\"Please provide a deck title.\");\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const aiSettings = getAIProviderSettings();\n      const token = localStorage.getItem('auth_token');\n\n      if (!token) {\n        throw new Error(\n          \"No authentication session found. Please log in again.\"\n        );\n      }\n\n      // Fetch document contents using the API\n      const documentContents: string[] = [];\n      for (const docId of selectedDocumentIds) {\n        try {\n          const response = await fetch(`${API_BASE_URL}/documents/${docId}/content`, {\n            method: \"GET\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n              Authorization: `Bearer ${token}`,\n            },\n          });\n\n          if (!response.ok) {\n            throw new Error(\n              `Failed to fetch document ${docId}: HTTP ${response.status}`\n            );\n          }\n\n          const textContent = await response.text();\n          if (textContent.trim()) {\n            documentContents.push(textContent);\n          }\n        } catch (err) {\n          console.error(`Error fetching document ${docId}:`, err);\n          throw new Error(\n            `Failed to fetch document ${docId}: ${\n              err instanceof Error ? err.message : \"Unknown error\"\n            }`\n          );\n        }\n      }\n\n      if (documentContents.length === 0) {\n        throw new Error(\"No valid document content found.\");\n      }\n\n      // Combine all document contents\n      const combinedContent = documentContents.join(\n        \"\\n\\n--- Document Separator ---\\n\\n\"\n      );\n\n      // Enhanced API payload with custom prompt support\n      const enhancedPayload = {\n        textContent: combinedContent,\n        documentId: selectedDocumentIds.join(\",\"), // Multiple document IDs\n        deckTitle: deckTitle,\n        customPrompt: customPrompt || undefined,\n        // aiSettings removed - credentials are retrieved from secure backend storage\n      };\n\n      console.log(\"📤 Sending flashcard generation request:\", enhancedPayload);\n      const sharedDeck = await generateFlashcardsAPI(enhancedPayload);\n      console.log(\"📥 Received flashcard generation response:\", sharedDeck);\n\n      if (!sharedDeck) {\n        throw new Error(\"No response received from flashcard generation API\");\n      }\n\n      const { deck, cards } = mapSharedDeckToClient(sharedDeck);\n\n      // Save flashcard set via Express.js backend\n      const authToken = localStorage.getItem('auth_token');\n\n      if (!authToken) {\n        throw new Error(\"No authentication session found. Please log in again.\");\n      }\n\n      const createSetPayload = {\n        name: deck.name,\n        description: deck.description || `Generated from ${selectedDocumentIds.length} document(s)`,\n        study_document_id: selectedDocumentIds[0] || null, // Use first document ID\n        flashcards: cards.map((card) => ({\n          front_text: card.question,\n          back_text: card.answer,\n        })),\n      };\n\n      const response = await fetch(`${API_BASE_URL}/flashcard-sets`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${authToken}`,\n        },\n        body: JSON.stringify(createSetPayload),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`\n        );\n      }\n\n      const savedFlashcardSet = await response.json();\n\n      // Update deck with the correct backend ID and card count\n      const updatedDeck = {\n        ...deck,\n        id: savedFlashcardSet.id,\n        name: savedFlashcardSet.name,\n        totalCards: cards.length,\n        dueTodayCount: cards.length,\n      };\n\n      setGeneratedDeck(updatedDeck);\n      setUserDecks((prev) => [updatedDeck, ...prev]);\n\n      // Clear selections\n      setSelectedDocumentIds([]);\n      setCustomPrompt(\"\");\n      setDeckTitle(\"\");\n\n      toast({\n        title: \"Success!\",\n        description: `Generated \"${deck.name}\" with ${cards.length} flashcards from ${selectedDocumentIds.length} document(s).`,\n      });\n    } catch (e) {\n      console.error(\"Error generating flashcards from documents:\", e);\n      setError(\n        e instanceof Error\n          ? e.message\n          : \"Failed to generate flashcards from documents.\"\n      );\n    }\n    setIsLoading(false);\n  };\n\n  const handleManageDeck = (deckId: string) => {\n    navigate(`/flashcards/edit/${deckId}`);\n  };\n\n  const handleDeleteDeck = async (deckId: string, deckName: string) => {\n    if (\n      !window.confirm(\n        `Are you sure you want to delete the deck \"${deckName}\"? All flashcards in this deck will also be deleted.`\n      )\n    )\n      return;\n\n    try {\n      const token = localStorage.getItem('auth_token');\n\n      if (!token) {\n        throw new Error(\"No authentication session found. Please log in again.\");\n      }\n\n      const response = await fetch(`${API_BASE_URL}/flashcard-sets/${deckId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(\n          errorData.error || `HTTP ${response.status}: ${response.statusText}`\n        );\n      }\n\n      // Remove from local state\n      setUserDecks((prev) => prev.filter((deck) => deck.id !== deckId));\n\n      toast({\n        title: \"Success\",\n        description: `Deck \"${deckName}\" has been deleted.`,\n      });\n    } catch (error) {\n      console.error(\"Error deleting deck:\", error);\n      toast({\n        title: \"Error\",\n        description: error instanceof Error ? error.message : \"Failed to delete the deck. Please try again.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n  const handleGenerateQuizFromDeck = async (\n    deck: FlashcardDeck,\n    options: QuizGenerationOptions\n  ) => {\n    if (!isAIConfigured) {\n      toast({\n        title: \"AI Provider Not Configured\",\n        description:\n          \"Please configure your AI provider settings to generate quizzes.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    if (generatingQuizForDeck) {\n      return; // Prevent multiple simultaneous generations\n    }\n\n    setGeneratingQuizForDeck(deck.id);\n\n    try {\n      // Fetch flashcards from backend API instead of local storage\n      const token = localStorage.getItem('auth_token');\n\n      if (!token) {\n        throw new Error(\n          \"No authentication session found. Please log in again.\"\n        );\n      }\n\n      console.log(\"🔍 Fetching flashcards for deck:\", {\n        deckId: deck.id,\n        deckName: deck.name,\n        totalCards: deck.totalCards\n      });\n\n      const response = await fetch(`${API_BASE_URL}/flashcard-sets/${deck.id}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(\n          errorData.error || `Failed to fetch flashcards: ${response.statusText}`\n        );\n      }\n\n      const flashcardSetData = await response.json();\n      const flashcards = flashcardSetData.flashcards || [];\n\n      console.log(\"📥 Fetched flashcards:\", {\n        deckId: deck.id,\n        flashcardCount: flashcards.length,\n        flashcardSetData: flashcardSetData\n      });\n\n      if (flashcards.length === 0) {\n        toast({\n          title: \"No Flashcards Found\",\n          description: \"This deck has no flashcards to generate a quiz from.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Map flashcards from backend format to expected format\n      const textContent = flashcards\n        .map((card: any) => `Question: ${card.front_text || card.question}\\nAnswer: ${card.back_text || card.answer}`)\n        .join(\"\\n\\n\");\n\n      const aiSettings = getAIProviderSettings();\n      const quizTitle = `Quiz: ${deck.name}`;\n      const numberOfQuestions = Math.min(\n        flashcards.length,\n        options.numberOfQuestions\n      );\n\n      console.log(\"🎯 Quiz Generation Debug Info:\", {\n        deckId: deck.id,\n        deckName: deck.name,\n        flashcardCount: flashcards.length,\n        textContentLength: textContent.length,\n        quizTitle,\n        numberOfQuestions,\n        aiProvider: aiSettings.provider,\n        aiModel: aiSettings.generationModel,\n        hasApiKey: !!aiSettings.apiKey,\n        sampleFlashcard: flashcards[0] // Log first flashcard for debugging\n      });\n      const requestBody = {\n        textContent,\n        quizName: quizTitle,\n        generationOptions: {\n          numberOfQuestions,\n          questionTypes: options.questionTypes,\n          customPrompt: options.customPrompt,\n        },\n        // AI config is now handled by the backend using stored credentials\n      };\n\n      console.log(\"📤 Sending quiz generation request:\", {\n        url: `${API_BASE_URL}/quizzes/generate`,\n        method: \"POST\",\n        hasAuth: !!token,\n        bodyKeys: Object.keys(requestBody),\n      });\n\n      const generateQuizResponse = await fetch(`${API_BASE_URL}/quizzes/generate`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${token}`,\n        },\n        body: JSON.stringify(requestBody),\n      });\n\n      console.log(\"📥 Quiz generation response:\", {\n        status: generateQuizResponse.status,\n        statusText: generateQuizResponse.statusText,\n        ok: generateQuizResponse.ok,\n      });\n\n      if (!generateQuizResponse.ok) {\n        const errorData = await generateQuizResponse.json().catch(() => ({}));\n        console.error(\"❌ Quiz generation error details:\", errorData);\n        throw new Error(\n          errorData.error ||\n            errorData.message ||\n            `HTTP ${generateQuizResponse.status}: ${generateQuizResponse.statusText}`\n        );\n      }\n\n      const quizData = await generateQuizResponse.json();\n      console.log(\"✅ Quiz generated successfully:\", {\n        quizId: quizData.quizId || quizData.id,\n        hasQuiz: !!quizData.quiz,\n      });\n\n      const finalQuizId = quizData.quizId || quizData.id;\n      if (!finalQuizId) {\n        throw new Error(\"No quiz ID returned from server\");\n      }\n      toast({\n        title: \"Quiz Generated Successfully\",\n        description: `Created \"${quizTitle}\" with ${numberOfQuestions} questions!`,\n      });\n\n      // Navigate to quizzes page and show generating notification\n      navigate(\"/quizzes\");\n\n      // Show a temporary notification that the quiz is being added to the list\n      setTimeout(() => {\n        toast({\n          title: \"Quiz Ready\",\n          description: `\"${quizTitle}\" has been added to your quizzes!`,\n        });\n      }, 1000);\n    } catch (error: any) {\n      console.error(\"❌ Error generating quiz:\", error);\n      console.error(\"Error stack:\", error.stack);\n      toast({\n        title: \"Quiz Generation Failed\",\n        description:\n          error.message || \"Failed to generate quiz from flashcards.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setGeneratingQuizForDeck(null);\n    }\n  };\n\n  return (\n    <AppLayout title=\"Flashcards\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h1 className=\"text-3xl font-bold text-slate-800 dark:text-slate-100\">\n            Flashcard Sets\n          </h1>\n        </div>\n\n        {/* Create New Deck Card */}\n        <Card className=\"mb-8 bg-purple-900 bg-opacity-20 border-purple-700\">\n          <CardHeader>\n            <CardTitle className=\"text-slate-100\">Create New Deck</CardTitle>\n            <CardDescription className=\"text-purple-300\">\n              Select documents and customize AI generation settings, or upload a\n              new document.\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-6\">\n              {/* Deck Title */}\n              <div>\n                <Label htmlFor=\"deck-title\" className=\"text-slate-300\">\n                  Deck Title*\n                </Label>\n                <Input\n                  id=\"deck-title\"\n                  type=\"text\"\n                  value={deckTitle}\n                  onChange={(e) => setDeckTitle(e.target.value)}\n                  placeholder=\"e.g., Chapter 1 Flashcards\"\n                  disabled={isLoading || uploadingFile}\n                  className=\"mt-1 !bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500 focus:ring-purple-500 focus:border-purple-500\"\n                />\n              </div>\n\n              {/* File Upload Section */}\n              <div>\n                <Label htmlFor=\"file-upload\" className=\"text-slate-300\">\n                  Upload New Document (Optional)\n                </Label>\n                <Input\n                  id=\"file-upload\"\n                  type=\"file\"\n                  accept=\".pdf,.docx,.txt,.md\"\n                  onChange={handleFileUpload}\n                  disabled={isLoading || uploadingFile}\n                  className=\"mt-1 !bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500 file:border-0 file:bg-transparent file:text-purple-400\"\n                />\n                <p className=\"text-xs text-slate-400 mt-1\">\n                  {uploadingFile\n                    ? \"Uploading and processing...\"\n                    : \"Upload a PDF, DOCX, TXT, or MD file to add to your documents\"}\n                </p>\n              </div>\n\n              {/* Document Selection */}\n              <div>\n                <Label className=\"text-slate-300 mb-2 block\">\n                  Select Documents for AI Generation*\n                </Label>\n                {loadingDocuments ? (\n                  <div className=\"flex justify-center items-center py-4\">\n                    <Spinner size=\"sm\" />\n                    <span className=\"ml-2 text-purple-300\">\n                      Loading documents...\n                    </span>\n                  </div>\n                ) : availableDocuments.length === 0 ? (\n                  <p className=\"text-sm text-purple-300 p-3 bg-slate-700 rounded-md\">\n                    No extracted documents available. Please upload and process\n                    documents first.\n                  </p>\n                ) : (\n                  <div className=\"max-h-60 overflow-y-auto space-y-2 p-3 bg-slate-700/50 rounded-md border border-slate-600\">\n                    {availableDocuments.map((doc) => (\n                      <div\n                        key={doc.id}\n                        className=\"flex items-center space-x-2 p-2 bg-slate-700 rounded hover:bg-slate-600/70 transition-colors\"\n                      >\n                        <Checkbox\n                          id={`ai-doc-${doc.id}`}\n                          checked={selectedDocumentIds.includes(doc.id)}\n                          onCheckedChange={() =>\n                            handleDocumentSelection(doc.id)\n                          }\n                          disabled={isLoading}\n                        />\n                        <Label\n                          htmlFor={`ai-doc-${doc.id}`}\n                          className=\"font-normal text-purple-300 cursor-pointer flex-1 truncate\"\n                          title={doc.file_name}\n                        >\n                          {doc.file_name}\n                        </Label>\n                      </div>\n                    ))}\n                  </div>\n                )}\n                {selectedDocumentIds.length > 0 && (\n                  <p className=\"text-xs text-purple-400 mt-1\">\n                    {selectedDocumentIds.length} document(s) selected.\n                  </p>\n                )}\n              </div>\n\n              {/* Custom Prompt */}\n              <div>\n                <Label htmlFor=\"custom-prompt\" className=\"text-slate-300\">\n                  Custom Prompt (Optional)\n                </Label>\n                <Textarea\n                  id=\"custom-prompt\"\n                  value={customPrompt}\n                  onChange={(e) => setCustomPrompt(e.target.value)}\n                  placeholder=\"e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'\"\n                  rows={3}\n                  className=\"mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\"\n                  disabled={isLoading}\n                />\n                <p className=\"text-xs text-purple-400 mt-1\">\n                  Add specific instructions for the AI on what kind of\n                  flashcards you want.\n                </p>\n              </div>\n\n              {error && (\n                <Alert variant=\"destructive\">\n                  <Terminal className=\"h-4 w-4\" />\n                  <AlertTitle>Error</AlertTitle>\n                  <AlertDescription>{error}</AlertDescription>\n                </Alert>\n              )}\n\n              <Button\n                onClick={handleAiFlashcardGeneration}\n                disabled={\n                  isLoading ||\n                  uploadingFile ||\n                  selectedDocumentIds.length === 0 ||\n                  !deckTitle.trim() ||\n                  !isAIConfigured\n                }\n                className=\"w-full\"\n              >\n                {isLoading\n                  ? \"Generating Flashcards...\"\n                  : \"Generate Flashcards with AI\"}\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n        {generatedDeck && (\n          <Card className=\"mb-8 bg-green-900 bg-opacity-20 border-green-700\">\n            <CardHeader>\n              <CardTitle className=\"text-green-300\">Deck Generated!</CardTitle>\n              <CardDescription className=\"text-green-400\">\n                {generatedDeck.totalCards} cards ready.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Link href={`/flashcards/${generatedDeck.id}`}>\n                <Button>Review Deck</Button>\n              </Link>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Existing Decks */}\n        <h2 className=\"text-2xl font-semibold mb-4 text-slate-100\">My Decks</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {userDecks.map((deck) => (\n            <Card\n              key={deck.id}\n              className=\"bg-purple-800 bg-opacity-20 border-purple-700\"\n            >\n              <CardHeader>\n                <CardTitle className=\"text-slate-200\">{deck.name}</CardTitle>\n              </CardHeader>\n              <CardContent className=\"flex flex-col space-y-4\">\n                <p className=\"text-slate-400\">{deck.totalCards} Cards</p>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  {\" \"}\n                  {/* 2x2 grid with consistent spacing */}\n                  {/* Row 1: Review & Generate Quiz */}\n                  <Link href={`/flashcards/${deck.id}`} className=\"w-full\">\n                    <Button className=\"w-full h-10 bg-purple-600 hover:bg-purple-700 text-white cursor-pointer\">\n                      Review\n                    </Button>\n                  </Link>\n                  <QuizGenerationPopup\n                    trigger={\n                      <Button\n                        disabled={generatingQuizForDeck === deck.id}\n                        className=\"w-full h-10 bg-purple-600 hover:bg-purple-700 text-white cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        {generatingQuizForDeck === deck.id\n                          ? \"Generating...\"\n                          : \"Generate Quiz\"}\n                      </Button>\n                    }\n                    onGenerate={(options) =>\n                      handleGenerateQuizFromDeck(deck, options)\n                    }\n                    isGenerating={generatingQuizForDeck === deck.id}\n                    disabled={generatingQuizForDeck === deck.id}\n                    maxQuestions={deck.totalCards || 20}\n                  />\n                  {/* Row 2: Manage & Delete */}\n                  <Button\n                    onClick={() => handleManageDeck(deck.id)}\n                    className=\"w-full h-10 bg-purple-600 hover:bg-purple-700 text-white cursor-pointer\"\n                  >\n                    <FilePenLine size={16} className=\"mr-1\" /> Manage\n                  </Button>\n                  <Button\n                    variant=\"destructive\"\n                    onClick={() => handleDeleteDeck(deck.id, deck.name)}\n                    className=\"w-full h-10 cursor-pointer\"\n                  >\n                    <Trash2 size={16} className=\"mr-1\" /> Delete\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n          {userDecks.length === 0 && (\n            <p className=\"text-slate-400\">No decks yet. Generate one above!</p>\n          )}\n        </div>\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default FlashcardsPage;\n"}