{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/QuizEditPage.tsx"}, "originalCode": "import React, { useState, useEffect } from \"react\";\nimport { useParams, useLocation } from \"wouter\";\nimport AppLayout from \"@/components/layout/AppLayout\";\nimport { QuizQuestionManager } from \"@/components/quiz/QuizQuestionManager\";\nimport { ErrorBoundary } from \"@/components/common/ErrorBoundary\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { Tables } from \"@/types/supabase\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { AlertCircle, ArrowLeft } from \"lucide-react\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ntype Quiz = Tables<\"quizzes\">;\n\nconst QuizEditPage: React.FC = () => {\n  const params = useParams<{ quizId: string }>();\n  const [, navigate] = useLocation();\n  const { user, signOut } = useAuth();\n  const quizId = params?.quizId;\n\n  const [quiz, setQuiz] = useState<Quiz | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchQuiz = async () => {\n      if (!user || !quizId) {\n        setLoading(false);\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n\n      try {\n        // Fetch quiz via backend API\n        const token = localStorage.getItem('auth_token');\n        const response = await fetch(`/api/quizzes/${quizId}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (!response.ok) {\n          if (response.status === 401) {\n            // Authentication failed - sign out and redirect\n            console.warn('Authentication failed in QuizEditPage, signing out...');\n            try {\n              await signOut();\n            } catch (signOutError) {\n              console.error('Error during signOut:', signOutError);\n            }\n            navigate('/');\n            return;\n          } else if (response.status === 404) {\n            setError(\"Quiz not found or you don't have permission to edit it.\");\n          } else {\n            const errorData = await response.json().catch(() => ({}));\n            throw new Error(errorData.error || 'Failed to fetch quiz');\n          }\n        } else {\n          const quizData = await response.json();\n          setQuiz(quizData);\n        }\n      } catch (err: any) {\n        console.error(\"Error fetching quiz:\", err);\n        setError(err.message || \"Failed to load quiz details.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchQuiz();\n  }, [user, quizId]);\n\n  const handleClose = () => {\n    navigate(\"/quizzes\");\n  };\n\n  if (loading) {\n    return (\n      <AppLayout title=\"Loading Quiz...\">\n        <div className=\"flex justify-center items-center py-12\">\n          <Spinner size=\"lg\" />\n          <span className=\"ml-4 text-xl text-slate-200\">Loading quiz...</span>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  if (error || !quiz) {\n    return (\n      <AppLayout title=\"Quiz Not Found\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <Card className=\"bg-slate-800 border-slate-700 max-w-md mx-auto\">\n            <CardContent className=\"p-6 text-center\">\n              <AlertCircle className=\"h-12 w-12 text-red-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-slate-200 mb-2\">\n                Quiz Not Found\n              </h3>\n              <p className=\"text-slate-400 mb-4\">\n                {error || \"The quiz you're looking for doesn't exist or you don't have permission to edit it.\"}\n              </p>\n              <Button\n                onClick={handleClose}\n                className=\"bg-purple-600 hover:bg-purple-700\"\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Quizzes\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  return (\n    <AppLayout title={`Edit: ${quiz.name}`}>\n      <div className=\"container mx-auto px-4 py-8\">\n        <QuizQuestionManager\n          selectedQuizId={quiz.id}\n          selectedQuizName={quiz.name}\n          studyDocumentId={quiz.study_document_id || undefined}\n          // This prop might not be used by QuizQuestionManager directly, but good to pass if needed for sub-components or future use\n          onClose={handleClose}\n        />\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default QuizEditPage; ", "modifiedCode": "import React, { useState, useEffect } from \"react\";\nimport { useParams, useLocation } from \"wouter\";\nimport AppLayout from \"@/components/layout/AppLayout\";\nimport { QuizQuestionManager } from \"@/components/quiz/QuizQuestionManager\";\nimport { ErrorBoundary } from \"@/components/common/ErrorBoundary\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { Tables } from \"@/types/supabase\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { AlertCircle, ArrowLeft } from \"lucide-react\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ntype Quiz = Tables<\"quizzes\">;\n\nconst QuizEditPage: React.FC = () => {\n  const params = useParams<{ quizId: string }>();\n  const [, navigate] = useLocation();\n  const { user, signOut } = useAuth();\n  const quizId = params?.quizId;\n\n  const [quiz, setQuiz] = useState<Quiz | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchQuiz = async () => {\n      if (!user || !quizId) {\n        setLoading(false);\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n\n      try {\n        // Fetch quiz via backend API\n        const token = localStorage.getItem('auth_token');\n        const response = await fetch(`/api/quizzes/${quizId}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (!response.ok) {\n          if (response.status === 401) {\n            // Authentication failed - sign out and redirect\n            console.warn('Authentication failed in QuizEditPage, signing out...');\n            try {\n              await signOut();\n            } catch (signOutError) {\n              console.error('Error during signOut:', signOutError);\n            }\n            navigate('/');\n            return;\n          } else if (response.status === 404) {\n            setError(\"Quiz not found or you don't have permission to edit it.\");\n          } else {\n            const errorData = await response.json().catch(() => ({}));\n            throw new Error(errorData.error || 'Failed to fetch quiz');\n          }\n        } else {\n          const quizData = await response.json();\n          setQuiz(quizData);\n        }\n      } catch (err: any) {\n        console.error(\"Error fetching quiz:\", err);\n        setError(err.message || \"Failed to load quiz details.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchQuiz();\n  }, [user, quizId]);\n\n  const handleClose = () => {\n    navigate(\"/quizzes\");\n  };\n\n  if (loading) {\n    return (\n      <AppLayout title=\"Loading Quiz...\">\n        <div className=\"flex justify-center items-center py-12\">\n          <Spinner size=\"lg\" />\n          <span className=\"ml-4 text-xl text-slate-200\">Loading quiz...</span>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  if (error || !quiz) {\n    return (\n      <AppLayout title=\"Quiz Not Found\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <Card className=\"bg-slate-800 border-slate-700 max-w-md mx-auto\">\n            <CardContent className=\"p-6 text-center\">\n              <AlertCircle className=\"h-12 w-12 text-red-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-slate-200 mb-2\">\n                Quiz Not Found\n              </h3>\n              <p className=\"text-slate-400 mb-4\">\n                {error || \"The quiz you're looking for doesn't exist or you don't have permission to edit it.\"}\n              </p>\n              <Button\n                onClick={handleClose}\n                className=\"bg-purple-600 hover:bg-purple-700\"\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Quizzes\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  return (\n    <AppLayout title={`Edit: ${quiz.name}`}>\n      <div className=\"container mx-auto px-4 py-8\">\n        <ErrorBoundary>\n          <QuizQuestionManager\n            selectedQuizId={quiz.id}\n            selectedQuizName={quiz.name}\n            studyDocumentId={quiz.study_document_id || undefined}\n            // This prop might not be used by QuizQuestionManager directly, but good to pass if needed for sub-components or future use\n            onClose={handleClose}\n          />\n        </ErrorBoundary>\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default QuizEditPage; "}