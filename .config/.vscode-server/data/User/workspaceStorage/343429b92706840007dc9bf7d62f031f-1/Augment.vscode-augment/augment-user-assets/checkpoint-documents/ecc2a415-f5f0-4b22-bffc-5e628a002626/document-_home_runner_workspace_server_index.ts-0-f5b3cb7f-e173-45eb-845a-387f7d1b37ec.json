{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/index.ts"}, "originalCode": "// Load environment variables only in development\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\n// Only load dotenv in development - moved to async function below\n\nimport express, { type Request, Response, NextFunction } from \"express\";\nimport cors from \"cors\";\nimport { registerRoutes } from \"./routes\";\nimport { setupVite, serveStatic, log } from \"./vite\";\n// fileURLToPath import moved to top of file\nimport { Readable } from \"stream\"; // Import Readable\n\n// Extend RequestInit to include Node.js specific duplex option\ninterface NodeRequestInit extends RequestInit {\n  duplex?: \"half\" | \"full\";\n}\nimport aiRoutes from \"./routes/aiRoutes\";\nimport quizRoutes from \"./routes/quizRoutes\";\nimport documentRoutes from \"./routes/documentRoutes\";\nimport flashcardDeckRoutes from \"./routes/flashcardDeckRoutes\";\nimport flashcardRoutes from \"./routes/flashcardRoutes\";\nimport flashcardSetRoutes from \"./routes/flashcardSetRoutes\";\nimport testRoutes from \"./routes/testRoutes\";\nimport healthRoutes from \"./routes/healthRoutes\";\nimport credentialsRoutes from \"./routes/credentialsRoutes\";\nimport authRoutes from \"./routes/authRoutes\";\n\nconst app = express();\n\n// CORS configuration for frontend-backend separation\napp.use(\n  cors({\n    // In production with Docker, the frontend and backend are served from the same origin\n    // In development, we need to allow cross-origin requests from the dev server\n    origin: process.env.NODE_ENV === \"production\"\n      ? true // Allow all origins in production since we're handling CORS at the container level\n      : process.env.FRONTEND_URL || \"http://localhost:3000\",\n    credentials: true,\n    methods: [\"GET\", \"POST\", \"PUT\", \"DELETE\", \"OPTIONS\"],\n    allowedHeaders: [\"Content-Type\", \"Authorization\"],\n  })\n);\n\napp.use(express.json({ limit: \"10mb\" }));\napp.use(express.urlencoded({ extended: false }));\n\n// Debug middleware to log API requests\napp.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    console.log(\n      `${req.method} ${req.path} - Body keys: ${Object.keys(req.body).join(\n        \", \"\n      )}`\n    );\n  }\n  next();\n});\n\n// Serve static files in production - moved to async function below\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      log(logLine);\n    }\n  });\n\n  next();\n});\n\nconsole.log(\"🔧 Mounting AI routes at /api\");\napp.use(\"/api\", aiRoutes);\nconsole.log(\"🔧 Mounting flashcard deck routes at /api/decks\");\napp.use(\"/api/decks\", flashcardDeckRoutes);\nconsole.log(\"🔧 Mounting flashcard routes at /api\");\napp.use(\"/api\", flashcardRoutes);\nconsole.log(\"🔧 Mounting test routes at /api\");\napp.use(\"/api\", testRoutes);\nconsole.log(\"🔧 Mounting health check routes at /api/health\");\napp.use(\"/api/health\", healthRoutes);\nconsole.log(\"🔧 Mounting credentials routes at /api/credentials\");\napp.use(\"/api/credentials\", credentialsRoutes);\nconsole.log(\"🔧 Mounting auth routes at /api/auth\");\napp.use(\"/api/auth\", authRoutes);\n\n// Helper function to convert Node.js stream to Web stream if needed\nasync function nodeStreamToWebStream(\n  nodeStream: NodeJS.ReadableStream\n): Promise<ReadableStream<Uint8Array>> {\n  return new ReadableStream({\n    start(controller) {\n      nodeStream.on(\"data\", (chunk) =>\n        controller.enqueue(new Uint8Array(chunk))\n      );\n      nodeStream.on(\"end\", () => controller.close());\n      nodeStream.on(\"error\", (err) => controller.error(err));\n    },\n  });\n}\n\n// Adapter middleware for Hono to Express\nconst honoAdapter = (honoApp: any, basePath: string = \"\") => {\n  return async (\n    req: express.Request,\n    res: express.Response,\n    next: express.NextFunction\n  ) => {\n    try {\n      const hasBody = req.method !== \"GET\" && req.method !== \"HEAD\" && req.body;\n      const requestBody = hasBody\n        ? req.body instanceof Readable\n          ? await nodeStreamToWebStream(req.body)\n          : new ReadableStream({\n              start: (controller) => {\n                controller.enqueue(Buffer.from(JSON.stringify(req.body)));\n                controller.close();\n              },\n            })\n        : undefined;\n\n      // Strip the base path from the URL for Hono\n      let honoPath = req.url;\n      if (basePath && req.url.startsWith(basePath)) {\n        honoPath = req.url.substring(basePath.length) || \"/\";\n      }\n\n      const webRequest = new Request(\n        `${req.protocol}://${req.get(\"host\")}${honoPath}`,\n        {\n          method: req.method,\n          headers: new Headers(req.headers as HeadersInit),\n          body: requestBody,\n          // Required for Node.js 18+ when sending a body\n          duplex: hasBody ? \"half\" : undefined,\n        } as NodeRequestInit\n      );\n\n      console.log(`[honoAdapter] Forwarding ${webRequest.method} ${webRequest.url} to Hono app. Path for Hono: ${honoPath}`);\n      const webResponse = await honoApp.fetch(webRequest);\n\n      res.status(webResponse.status);\n      webResponse.headers.forEach((value: string, key: string) => {\n        res.setHeader(key, value);\n      });\n\n      if (webResponse.body) {\n        const reader = webResponse.body.getReader();\n        const forwardStream = async () => {\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) break;\n            res.write(value);\n          }\n          res.end();\n        };\n        await forwardStream();\n      } else {\n        res.end();\n      }\n    } catch (error) {\n      next(error);\n    }\n  };\n};\n\n// Mount Express routes directly\nconsole.log(\"🔧 Mounting document routes at /api/documents\");\napp.use(\"/api/documents\", documentRoutes); // Mount document routes as Express routes\n\n// Mount Hono routes with the adapter\nconsole.log(\"🔧 Mounting quiz routes at /api/quizzes\");\napp.use(\"/api/quizzes\", honoAdapter(quizRoutes, \"/api/quizzes\")); // Mount quiz routes using Hono to Express adapter\n\nconsole.log(\"🔧 Mounting flashcard set routes at /api/flashcard-sets\");\napp.use(\"/api/flashcard-sets\", honoAdapter(flashcardSetRoutes, \"/api/flashcard-sets\")); // Mount flashcard set routes using Hono to Express adapter\n\n(async () => {\n  console.log(\"🚀 Starting server initialization...\");\n\n  // Load environment variables only in development\n  if (process.env.NODE_ENV !== \"production\") {\n    const { default: dotenv } = await import(\"dotenv\");\n    dotenv.config({ path: path.resolve(__dirname, \".env\") });\n  }\n\n  try {\n    // Set up Vite middleware for development only\n    if (process.env.NODE_ENV !== \"production\") {\n      console.log(\"🔧 Setting up Vite development middleware...\");\n      await setupVite(app);\n    } else {\n      console.log(\"🏭 Production mode: Skipping Vite middleware setup\");\n    }\n\n    const server = await registerRoutes(app);\n    console.log(\"✅ Routes registered successfully\");\n\n    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n      console.error(\"Unhandled error in Main Express App:\", err);\n      const status = err.status || err.statusCode || 500;\n      const responseBody: { error: string; message: string; stack?: string } = {\n        error: \"An unexpected server error occurred.\",\n        message: err.message || \"Internal Server Error\",\n      };\n      // Optionally include stack in development\n      if (app.get(\"env\") === \"development\" && err.stack) {\n        responseBody.stack = err.stack;\n      }\n      res.status(status).json(responseBody);\n      // Error is logged and JSON response sent. Do not call _next(err) or throw err to prevent Vite's HTML error.\n    });\n\n    // In production, serve the built frontend files\n    // In development, Vite middleware handles the frontend\n    if (process.env.NODE_ENV === \"production\") {\n      console.log(\"🏭 Production mode: Setting up static file serving\");\n      const { serveStatic } = await import(\"./vite\");\n      serveStatic(app);\n    } else {\n      console.log(\"🔧 Development mode: Vite middleware will handle frontend\");\n    }\n\n    // Get port from environment variable or use default\n    const port = parseInt(process.env.PORT || \"5000\", 10);\n    const isWindows = process.platform === \"win32\";\n    \n    log(`Starting server in ${process.env.NODE_ENV || 'development'} mode on port ${port}`);\n\n    const httpServer = server.listen(\n      {\n        port,\n        host: \"0.0.0.0\",\n        ...(isWindows ? {} : { reusePort: true }),\n      },\n      () => {\n        log(`✅ Backend server running on port ${port}`);\n        log(`🌐 API available at: http://localhost:${port}/api`);\n        log(`🔍 Health check: http://localhost:${port}/api/health`);\n        log(`🐛 Debug routes: http://localhost:${port}/api/debug/routes`);\n      }\n    );\n    \n    // Handle graceful shutdown\n    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];\n    signals.forEach((signal) => {\n      process.on(signal, () => {\n        log(`Received ${signal}, gracefully shutting down...`);\n        \n        // Set a timeout for forceful shutdown if graceful shutdown takes too long\n        const forcefulShutdownTimeout = setTimeout(() => {\n          log('Forceful shutdown timeout reached, exiting immediately!');\n          process.exit(1);\n        }, 30000); // 30 seconds timeout\n        \n        // Attempt graceful shutdown\n        httpServer.close(() => {\n          log('HTTP server closed successfully.');\n          clearTimeout(forcefulShutdownTimeout);\n          process.exit(0);\n        });\n      });\n    });\n  } catch (error: any) {\n    console.error(\"❌ Failed to start server:\", error);\n    console.error(\"Error details:\", error.message);\n    console.error(\"Stack trace:\", error.stack);\n    process.exit(1);\n  }\n})();\n", "modifiedCode": "// Load environment variables only in development\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\n// Only load dotenv in development - moved to async function below\n\nimport express, { type Request, Response, NextFunction } from \"express\";\nimport cors from \"cors\";\nimport { registerRoutes } from \"./routes\";\nimport { setupVite, serveStatic, log } from \"./vite\";\n// fileURLToPath import moved to top of file\nimport { Readable } from \"stream\"; // Import Readable\n\n// Extend RequestInit to include Node.js specific duplex option\ninterface NodeRequestInit extends RequestInit {\n  duplex?: \"half\" | \"full\";\n}\nimport aiRoutes from \"./routes/aiRoutes\";\nimport quizRoutes from \"./routes/quizRoutes\";\nimport documentRoutes from \"./routes/documentRoutes\";\nimport flashcardDeckRoutes from \"./routes/flashcardDeckRoutes\";\nimport flashcardRoutes from \"./routes/flashcardRoutes\";\nimport flashcardSetRoutes from \"./routes/flashcardSetRoutes\";\nimport testRoutes from \"./routes/testRoutes\";\nimport healthRoutes from \"./routes/healthRoutes\";\nimport credentialsRoutes from \"./routes/credentialsRoutes\";\nimport authRoutes from \"./routes/authRoutes\";\n\nconst app = express();\n\n// CORS configuration for frontend-backend separation\napp.use(\n  cors({\n    // In production with Docker, the frontend and backend are served from the same origin\n    // In development, we need to allow cross-origin requests from the dev server\n    origin: process.env.NODE_ENV === \"production\"\n      ? true // Allow all origins in production since we're handling CORS at the container level\n      : process.env.FRONTEND_URL || \"http://localhost:3000\",\n    credentials: true,\n    methods: [\"GET\", \"POST\", \"PUT\", \"DELETE\", \"OPTIONS\"],\n    allowedHeaders: [\"Content-Type\", \"Authorization\"],\n  })\n);\n\napp.use(express.json({ limit: \"10mb\" }));\napp.use(express.urlencoded({ extended: false }));\n\n// Debug middleware to log API requests\napp.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    console.log(\n      `${req.method} ${req.path} - Body keys: ${Object.keys(req.body).join(\n        \", \"\n      )}`\n    );\n  }\n  next();\n});\n\n// Serve static files in production - moved to async function below\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      log(logLine);\n    }\n  });\n\n  next();\n});\n\nconsole.log(\"🔧 Mounting AI routes at /api\");\napp.use(\"/api\", aiRoutes);\nconsole.log(\"🔧 Mounting flashcard deck routes at /api/decks\");\napp.use(\"/api/decks\", flashcardDeckRoutes);\nconsole.log(\"🔧 Mounting flashcard routes at /api\");\napp.use(\"/api\", flashcardRoutes);\nconsole.log(\"🔧 Mounting test routes at /api\");\napp.use(\"/api\", testRoutes);\nconsole.log(\"🔧 Mounting health check routes at /api/health\");\napp.use(\"/api/health\", healthRoutes);\nconsole.log(\"🔧 Mounting credentials routes at /api/credentials\");\napp.use(\"/api/credentials\", credentialsRoutes);\nconsole.log(\"🔧 Mounting auth routes at /api/auth\");\napp.use(\"/api/auth\", authRoutes);\n\n// Helper function to convert Node.js stream to Web stream if needed\nasync function nodeStreamToWebStream(\n  nodeStream: NodeJS.ReadableStream\n): Promise<ReadableStream<Uint8Array>> {\n  return new ReadableStream({\n    start(controller) {\n      nodeStream.on(\"data\", (chunk) =>\n        controller.enqueue(new Uint8Array(chunk))\n      );\n      nodeStream.on(\"end\", () => controller.close());\n      nodeStream.on(\"error\", (err) => controller.error(err));\n    },\n  });\n}\n\n// Adapter middleware for Hono to Express\nconst honoAdapter = (honoApp: any, basePath: string = \"\") => {\n  return async (\n    req: express.Request,\n    res: express.Response,\n    next: express.NextFunction\n  ) => {\n    try {\n      const hasBody = req.method !== \"GET\" && req.method !== \"HEAD\" && req.body;\n      const requestBody = hasBody\n        ? req.body instanceof Readable\n          ? await nodeStreamToWebStream(req.body)\n          : new ReadableStream({\n              start: (controller) => {\n                controller.enqueue(Buffer.from(JSON.stringify(req.body)));\n                controller.close();\n              },\n            })\n        : undefined;\n\n      // Strip the base path from the URL for Hono\n      let honoPath = req.url;\n      if (basePath && req.url.startsWith(basePath)) {\n        honoPath = req.url.substring(basePath.length) || \"/\";\n      }\n\n      const webRequest = new Request(\n        `${req.protocol}://${req.get(\"host\")}${honoPath}`,\n        {\n          method: req.method,\n          headers: new Headers(req.headers as HeadersInit),\n          body: requestBody,\n          // Required for Node.js 18+ when sending a body\n          duplex: hasBody ? \"half\" : undefined,\n        } as NodeRequestInit\n      );\n\n      console.log(`[honoAdapter] Forwarding ${webRequest.method} ${webRequest.url} to Hono app. Path for Hono: ${honoPath}`);\n      const webResponse = await honoApp.fetch(webRequest);\n\n      res.status(webResponse.status);\n      webResponse.headers.forEach((value: string, key: string) => {\n        res.setHeader(key, value);\n      });\n\n      if (webResponse.body) {\n        const reader = webResponse.body.getReader();\n        const forwardStream = async () => {\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) break;\n            res.write(value);\n          }\n          res.end();\n        };\n        await forwardStream();\n      } else {\n        res.end();\n      }\n    } catch (error) {\n      next(error);\n    }\n  };\n};\n\n// Mount Express routes directly\nconsole.log(\"🔧 Mounting document routes at /api/documents\");\napp.use(\"/api/documents\", documentRoutes); // Mount document routes as Express routes\n\n// Mount Hono routes with the adapter\nconsole.log(\"🔧 Mounting quiz routes at /api/quizzes\");\napp.use(\"/api/quizzes\", honoAdapter(quizRoutes, \"/api/quizzes\")); // Mount quiz routes using Hono to Express adapter\n\nconsole.log(\"🔧 Mounting flashcard set routes at /api/flashcard-sets\");\napp.use(\"/api/flashcard-sets\", honoAdapter(flashcardSetRoutes, \"/api/flashcard-sets\")); // Mount flashcard set routes using Hono to Express adapter\n\n(async () => {\n  console.log(\"🚀 Starting server initialization...\");\n\n  // Load environment variables only in development\n  if (process.env.NODE_ENV !== \"production\") {\n    const { default: dotenv } = await import(\"dotenv\");\n    dotenv.config({ path: path.resolve(__dirname, \".env\") });\n  }\n\n  try {\n    // Set up Vite middleware for development only\n    if (process.env.NODE_ENV !== \"production\") {\n      console.log(\"🔧 Setting up Vite development middleware...\");\n      await setupVite(app);\n    } else {\n      console.log(\"🏭 Production mode: Skipping Vite middleware setup\");\n    }\n\n    const server = await registerRoutes(app);\n    console.log(\"✅ Routes registered successfully\");\n\n    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n      console.error(\"Unhandled error in Main Express App:\", err);\n      const status = err.status || err.statusCode || 500;\n      const responseBody: { error: string; message: string; stack?: string } = {\n        error: \"An unexpected server error occurred.\",\n        message: err.message || \"Internal Server Error\",\n      };\n      // Optionally include stack in development\n      if (app.get(\"env\") === \"development\" && err.stack) {\n        responseBody.stack = err.stack;\n      }\n      res.status(status).json(responseBody);\n      // Error is logged and JSON response sent. Do not call _next(err) or throw err to prevent Vite's HTML error.\n    });\n\n    // In production, serve the built frontend files\n    // In development, Vite middleware handles the frontend\n    if (process.env.NODE_ENV === \"production\") {\n      console.log(\"🏭 Production mode: Setting up static file serving\");\n      const { serveStatic } = await import(\"./vite\");\n      serveStatic(app);\n    } else {\n      console.log(\"🔧 Development mode: Vite middleware will handle frontend\");\n    }\n\n    // Get port from environment variable or use default\n    const port = parseInt(process.env.PORT || \"5000\", 10);\n    const isWindows = process.platform === \"win32\";\n    \n    log(`Starting server in ${process.env.NODE_ENV || 'development'} mode on port ${port}`);\n\n    const httpServer = server.listen(\n      {\n        port,\n        host: \"0.0.0.0\",\n        ...(isWindows ? {} : { reusePort: true }),\n      },\n      () => {\n        log(`✅ Backend server running on port ${port}`);\n        log(`🌐 API available at: http://localhost:${port}/api`);\n        log(`🔍 Health check: http://localhost:${port}/api/health`);\n        log(`🐛 Debug routes: http://localhost:${port}/api/debug/routes`);\n      }\n    );\n    \n    // Handle graceful shutdown\n    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];\n    signals.forEach((signal) => {\n      process.on(signal, () => {\n        log(`Received ${signal}, gracefully shutting down...`);\n        \n        // Set a timeout for forceful shutdown if graceful shutdown takes too long\n        const forcefulShutdownTimeout = setTimeout(() => {\n          log('Forceful shutdown timeout reached, exiting immediately!');\n          process.exit(1);\n        }, 30000); // 30 seconds timeout\n        \n        // Attempt graceful shutdown\n        httpServer.close(() => {\n          log('HTTP server closed successfully.');\n          clearTimeout(forcefulShutdownTimeout);\n          process.exit(0);\n        });\n      });\n    });\n  } catch (error: any) {\n    console.error(\"❌ Failed to start server:\", error);\n    console.error(\"Error details:\", error.message);\n    console.error(\"Stack trace:\", error.stack);\n    process.exit(1);\n  }\n})();\n"}