{"path": {"rootPath": "/home/<USER>/workspace", "relPath": ".replit"}, "originalCode": "# Specifies the Nix environment and Replit modules to use.\n# 'nodejs-20' provides the Node.js runtime.\n# 'web' is necessary for Replit to serve your application on standard HTTP/HTTPS ports.\n# Removed 'postgresql-16' as your Drizzle and Supabase configurations point to an external Supabase DB.\nmodules = [\"nodejs-20\", \"web\"]\n\n# Command to execute when the \"Run\" button in the Replit IDE is pressed.\n# 'npm run dev' typically starts your development servers (frontend and backend).\nrun = \"npm run dev\"\n\n# Files and directories to hide from the Replit file explorer.\nhidden = [\".config\", \".git\", \"generated-icon.png\", \"node_modules\", \"dist\"]\n\n[nix]\n# Specifies the Nix channel for environment reproducibility.\nchannel = \"stable-24_05\"\npackages = [\"supabase-cli\"]\n\n[deployment]\n# Configures the deployment target on Replit.\ndeploymentTarget = \"autoscale\"\nbuild = [\"sh\", \"-c\", \"npm run build\"]\nrun = [\"sh\", \"-c\", \"NODE_ENV=production PORT=80 tsx server/index.ts\"]\n\n# Health check endpoint for monitoring\nhealthcheck = \"/api/health\"\n\n# Environment variables for production deployment\n[deployment.env]\nNODE_ENV = \"production\"\nPORT = \"80\"\n\n# SPA Fallback: Rewrites all non-file paths to /index.html for client-side routing.\n# Your server/index.ts also handles this, but this Replit rule can act as a fallback.\n[[deployment.rewrites]]\nfrom = \"/*\"\nto = \"/index.html\"\n\n# CORS Headers:\n# Your application (server/index.ts) already configures CORS using `app.use(cors(...))`.\n# The application-level CORS is more specific and generally preferred.\n# These global Replit-level headers might be redundant or overly permissive.\n# Consider removing these if your application's CORS handling is sufficient.\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Origin\"\nvalue = \"*\"\n\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Methods\"\nvalue = \"GET, POST, PUT, DELETE, OPTIONS\"\n\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Headers\"\nvalue = \"Content-Type, Authorization\"\n\n[[ports]]\nlocalPort = 80\nexternalPort = 80\n\n[[ports]]\nlocalPort = 3000\nexternalPort = 3000\n\n[[ports]]\nlocalPort = 3001\nexternalPort = 3001\n\n[[ports]]\nlocalPort = 5000\nexternalPort = 5001\n\n[[ports]]\nlocalPort = 24678\nexternalPort = 24678\n\n# Workflow for the \"Run\" button in the Replit IDE.\n[workflows]\nrunButton = \"Project\"\n\n[[workflows.workflow]]\nname = \"Project\"\nmode = \"parallel\"\nauthor = \"agent\"\n[[workflows.workflow.tasks]]\ntask = \"workflow.run\"\nargs = \"Start application\"\n\n[[workflows.workflow]]\nname = \"Start application\"\nauthor = \"agent\"\n[[workflows.workflow.tasks]]\ntask = \"shell.exec\"\nargs = \"npm run dev\" # This executes your development script.\nwaitForPort = 3000    # Waits for the Vite frontend dev server to be ready on port 3000.\n", "modifiedCode": "# Specifies the Nix environment and Replit modules to use.\n# 'nodejs-20' provides the Node.js runtime.\n# 'web' is necessary for Replit to serve your application on standard HTTP/HTTPS ports.\n# Removed 'postgresql-16' as your Drizzle and Supabase configurations point to an external Supabase DB.\nmodules = [\"nodejs-20\", \"web\"]\n\n# Command to execute when the \"Run\" button in the Replit IDE is pressed.\n# 'npm run dev' typically starts your development servers (frontend and backend).\nrun = \"npm run dev\"\n\n# Files and directories to hide from the Replit file explorer.\nhidden = [\".config\", \".git\", \"generated-icon.png\", \"node_modules\", \"dist\"]\n\n[nix]\n# Specifies the Nix channel for environment reproducibility.\nchannel = \"stable-24_05\"\npackages = [\"supabase-cli\"]\n\n[deployment]\n# Configures the deployment target on Replit.\ndeploymentTarget = \"autoscale\"\nbuild = [\"sh\", \"-c\", \"npm run build\"]\nrun = [\"sh\", \"-c\", \"NODE_ENV=production PORT=80 tsx server/index.ts\"]\n\n# Health check endpoint for monitoring\nhealthcheck = \"/api/health\"\n\n# Environment variables for production deployment\n[deployment.env]\nNODE_ENV = \"production\"\nPORT = \"80\"\n\n# SPA Fallback: Rewrites all non-file paths to /index.html for client-side routing.\n# Your server/index.ts also handles this, but this Replit rule can act as a fallback.\n[[deployment.rewrites]]\nfrom = \"/*\"\nto = \"/index.html\"\n\n# CORS Headers:\n# Your application (server/index.ts) already configures CORS using `app.use(cors(...))`.\n# The application-level CORS is more specific and generally preferred.\n# These global Replit-level headers might be redundant or overly permissive.\n# Consider removing these if your application's CORS handling is sufficient.\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Origin\"\nvalue = \"*\"\n\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Methods\"\nvalue = \"GET, POST, PUT, DELETE, OPTIONS\"\n\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Headers\"\nvalue = \"Content-Type, Authorization\"\n\n[[ports]]\nlocalPort = 80\nexternalPort = 80\n\n[[ports]]\nlocalPort = 3000\nexternalPort = 3000\n\n[[ports]]\nlocalPort = 3001\nexternalPort = 3001\n\n[[ports]]\nlocalPort = 5000\nexternalPort = 5001\n\n[[ports]]\nlocalPort = 24678\nexternalPort = 24678\n\n# Workflow for the \"Run\" button in the Replit IDE.\n[workflows]\nrunButton = \"Project\"\n\n[[workflows.workflow]]\nname = \"Project\"\nmode = \"parallel\"\nauthor = \"agent\"\n[[workflows.workflow.tasks]]\ntask = \"workflow.run\"\nargs = \"Start application\"\n\n[[workflows.workflow]]\nname = \"Start application\"\nauthor = \"agent\"\n[[workflows.workflow.tasks]]\ntask = \"shell.exec\"\nargs = \"npm run dev\" # This executes your development script.\nwaitForPort = 3000    # Waits for the Vite frontend dev server to be ready on port 3000.\n"}