{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/debug/checkCredentials.ts"}, "modifiedCode": "/**\n * Debug script to check user credentials in the database\n * Run this to verify that credentials are properly stored and accessible\n */\n\nimport { createClient } from '@supabase/supabase-js';\nimport { supabaseConfig } from '../config';\n\nconst supabase = createClient(supabaseConfig.url, supabaseConfig.serviceRoleKey);\n\nasync function checkCredentialsTable() {\n  console.log('🔍 Checking user_ai_credentials table...');\n  \n  try {\n    // First, check if the table exists and get its structure\n    const { data: tableInfo, error: tableError } = await supabase\n      .from('user_ai_credentials')\n      .select('*')\n      .limit(1);\n\n    if (tableError) {\n      console.error('❌ Error accessing user_ai_credentials table:', tableError);\n      return;\n    }\n\n    console.log('✅ Table exists and is accessible');\n\n    // Get all records to see what's stored\n    const { data: allCredentials, error: allError } = await supabase\n      .from('user_ai_credentials')\n      .select('user_id, provider, base_url, extraction_model, generation_model, created_at, updated_at');\n\n    if (allError) {\n      console.error('❌ Error fetching all credentials:', allError);\n      return;\n    }\n\n    console.log(`📊 Found ${allCredentials?.length || 0} credential records:`);\n    allCredentials?.forEach((cred, index) => {\n      console.log(`  ${index + 1}. User: ${cred.user_id}, Provider: ${cred.provider}, Base URL: ${cred.base_url}`);\n      console.log(`     Models: extraction=${cred.extraction_model}, generation=${cred.generation_model}`);\n      console.log(`     Created: ${cred.created_at}, Updated: ${cred.updated_at}`);\n    });\n\n    // Check for OpenRouter specifically\n    const { data: openRouterCreds, error: openRouterError } = await supabase\n      .from('user_ai_credentials')\n      .select('*')\n      .eq('provider', 'OpenRouter');\n\n    if (openRouterError) {\n      console.error('❌ Error fetching OpenRouter credentials:', openRouterError);\n      return;\n    }\n\n    console.log(`🎯 Found ${openRouterCreds?.length || 0} OpenRouter credential records`);\n\n  } catch (error) {\n    console.error('❌ Unexpected error:', error);\n  }\n}\n\nasync function checkSpecificUser(userId: string) {\n  console.log(`🔍 Checking credentials for specific user: ${userId}`);\n  \n  try {\n    const { data, error } = await supabase\n      .from('user_ai_credentials')\n      .select('*')\n      .eq('user_id', userId)\n      .eq('provider', 'OpenRouter')\n      .single();\n\n    if (error) {\n      if (error.code === 'PGRST116') {\n        console.log(`📭 No OpenRouter credentials found for user ${userId}`);\n      } else {\n        console.error('❌ Error fetching user credentials:', error);\n      }\n      return;\n    }\n\n    console.log(`✅ Found OpenRouter credentials for user ${userId}:`, {\n      provider: data.provider,\n      baseUrl: data.base_url,\n      extractionModel: data.extraction_model,\n      generationModel: data.generation_model,\n      hasEncryptedKey: !!data.encrypted_api_key,\n      hasIV: !!data.encryption_iv,\n      hasTag: !!data.encryption_tag,\n      createdAt: data.created_at,\n      updatedAt: data.updated_at\n    });\n\n  } catch (error) {\n    console.error('❌ Unexpected error checking user credentials:', error);\n  }\n}\n\n// Main execution\nasync function main() {\n  console.log('🚀 Starting credentials diagnostic...\\n');\n  \n  await checkCredentialsTable();\n  \n  console.log('\\n' + '='.repeat(50));\n  console.log('To check a specific user, call:');\n  console.log('checkSpecificUser(\"your-user-id-here\")');\n  console.log('='.repeat(50));\n}\n\n// Export functions for manual testing\nexport { checkCredentialsTable, checkSpecificUser };\n\n// Run if called directly\nif (require.main === module) {\n  main().catch(console.error);\n}\n"}