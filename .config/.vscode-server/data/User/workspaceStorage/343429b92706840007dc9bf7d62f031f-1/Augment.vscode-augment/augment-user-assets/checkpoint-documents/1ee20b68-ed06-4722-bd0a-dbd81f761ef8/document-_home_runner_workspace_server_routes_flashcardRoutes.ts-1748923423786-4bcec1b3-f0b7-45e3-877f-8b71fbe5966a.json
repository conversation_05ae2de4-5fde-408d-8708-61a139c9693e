{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/flashcardRoutes.ts"}, "originalCode": "import { Hono } from \"hono\";\nimport type { Context } from \"hono\";\nimport type { AppVariables } from \"../middleware/authMiddleware\";\n\nconst flashcardRoutes = new Hono<{ Variables: AppVariables }>();\n\n// POST /api/decks/:deckId/flashcards - Create a new flashcard in a deck\nrouter.post(\"/decks/:deckId/flashcards\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n  const { deckId } = req.params;\n  try {\n    // Verify deck ownership\n    const deck = await db\n      .select()\n      .from(flashcardDecks)\n      .where(\n        and(\n          eq(flashcardDecks.id, Number(deckId)),\n          eq(flashcardDecks.userId, user.id)\n        )\n      );\n    if (!deck || deck.length === 0) {\n      return res.status(404).json({ error: \"Deck not found or not owned by user\" });\n    }\n    const body = req.body;\n    const parsedBody = insertFlashcardSchema\n      .omit({ deckId: true })\n      .safeParse(body);\n    if (!parsedBody.success) {\n      return res\n        .status(400)\n        .json({ error: \"Invalid flashcard data\", details: parsedBody.error.flatten() });\n    }\n    const cardData = {\n      ...parsedBody.data,\n      deckId: Number(deckId),\n      createdAt: Date.now(),\n    };\n    const newCard = await db.insert(flashcards).values(cardData).returning();\n    if (!newCard || newCard.length === 0) {\n      return res.status(500).json({ error: \"Failed to create flashcard\" });\n    }\n    return res.status(201).json(newCard[0]);\n  } catch (error: any) {\n    console.error(\"Error creating flashcard:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to create flashcard\", details: error.message });\n  }\n});\n\n// GET /api/decks/:deckId/flashcards - Get all flashcards for a deck\nrouter.get(\"/decks/:deckId/flashcards\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n  const { deckId } = req.params;\n  try {\n    // Verify deck ownership\n    const deck = await db\n      .select()\n      .from(flashcardDecks)\n      .where(\n        and(\n          eq(flashcardDecks.id, Number(deckId)),\n          eq(flashcardDecks.userId, user.id)\n        )\n      );\n    if (!deck || deck.length === 0) {\n      return res.status(404).json({ error: \"Deck not found or not owned by user\" });\n    }\n    const cards = await db\n      .select()\n      .from(flashcards)\n      .where(eq(flashcards.deckId, Number(deckId)));\n    return res.status(200).json(cards);\n  } catch (error: any) {\n    console.error(\"Error fetching flashcards:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to fetch flashcards\", details: error.message });\n  }\n});\n\nexport default router;\n", "modifiedCode": "import { Hono } from \"hono\";\nimport type { Context } from \"hono\";\nimport type { AppVariables } from \"../middleware/authMiddleware\";\n\nconst flashcardRoutes = new Hono<{ Variables: AppVariables }>();\n\n// Route to update an individual flashcard\nflashcardRoutes.put(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    const body = await c.req.json();\n    const { front_text, back_text } = body;\n\n    if (!front_text || !back_text) {\n      return c.json({ error: \"Both front_text and back_text are required\" }, 400);\n    }\n\n    // First verify the flashcard exists and user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"id, user_id, set_id\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error checking flashcard ownership:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    // Update the flashcard\n    const { data: updatedFlashcard, error: updateError } = await supabase\n      .from(\"flashcards\")\n      .update({\n        front_text: front_text.trim(),\n        back_text: back_text.trim(),\n        updated_at: new Date().toISOString(),\n      })\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .select()\n      .single();\n\n    if (updateError) {\n      console.error(\"Error updating flashcard:\", updateError);\n      return c.json(\n        { error: \"Failed to update flashcard\", details: updateError.message },\n        500\n      );\n    }\n\n    return c.json({\n      message: \"Flashcard updated successfully\",\n      flashcard: updatedFlashcard,\n    }, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard update:\", error);\n    return c.json({ error: error.message || \"Failed to update flashcard\" }, 500);\n  }\n});\n"}