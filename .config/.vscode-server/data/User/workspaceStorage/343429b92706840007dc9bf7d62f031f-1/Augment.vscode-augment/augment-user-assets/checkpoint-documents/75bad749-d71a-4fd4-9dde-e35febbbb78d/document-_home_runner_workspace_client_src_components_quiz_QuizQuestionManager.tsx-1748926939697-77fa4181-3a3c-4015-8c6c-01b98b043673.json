{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuizQuestionManager.tsx"}, "originalCode": "import React, { useEffect, useState, useRef, useCallback } from \"react\";\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables } from \"../../types/supabase\";\nimport { QuestionForm } from \"./QuestionForm\";\nimport { AiQuestionGenerator } from \"./AiQuestionGenerator\";\nimport { QuestionsList, QuestionsListProps } from \"./QuestionsList\";\nimport { Button } from \"@/components/ui/button\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\ninterface QuizQuestionManagerProps {\n  selectedQuizId: string;\n  selectedQuizName: string;\n  studyDocumentId?: string;\n  onClose: () => void;\n}\n\nexport const QuizQuestionManager: React.FC<QuizQuestionManagerProps> = ({\n  selectedQuizId,\n  selectedQuizName,\n  studyDocumentId,\n  onClose,\n}) => {\n  const { user, signOut } = useAuth();\n  const [questions, setQuestions] = useState<QuizQuestion[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(\n    null\n  );\n\n  // Refs to manage polling and prevent memory leaks\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const mountedRef = useRef(true);\n  const retryCountRef = useRef(0);\n  const maxRetries = 3;\n\n  const fetchQuestions = async () => {\n    if (!user || !selectedQuizId) return;\n    setLoading(true);\n    try {\n      const response = await fetch(`/api/quizzes/${selectedQuizId}/questions`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,\n        },\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || 'Failed to fetch questions');\n      }\n\n      const data = await response.json();\n      setQuestions(data || []);\n      setError(null);\n    } catch (err: any) {\n      setError(err.message || \"Failed to fetch questions.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchQuestions();\n\n    // Poll for updates every 5 seconds instead of realtime\n    const interval = setInterval(() => {\n      if (user && selectedQuizId) {\n        fetchQuestions();\n      }\n    }, 5000);\n\n    return () => {\n      clearInterval(interval);\n    };\n  }, [user, selectedQuizId]);\n\n  const handleQuestionSaved = () => {\n    setEditingQuestion(null);\n    fetchQuestions();\n  };\n\n  const handleCancelEdit = () => {\n    setEditingQuestion(null);\n  };\n\n  const handleGenerationComplete = () => {\n        fetchQuestions();\n  };\n\n  if (loading)\n    return (\n      <p className=\"text-slate-300\">\n        Loading questions for \"{selectedQuizName}\"...\n      </p>\n    );\n  if (error) return <p className=\"text-red-400\">Error: {error}</p>;\n\n  return (\n    <div className=\"mt-5 space-y-6\">\n      <div className=\"bg-slate-800 shadow-lg rounded-lg border border-slate-700 p-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <div>\n            <h4 className=\"text-2xl font-bold text-slate-200 mb-2\">\n              Manage Questions\n            </h4>\n            <p className=\"text-slate-400 text-sm\">\n              Quiz:{\" \"}\n              <span className=\"text-purple-400 font-medium\">\n                {selectedQuizName}\n              </span>\n            </p>\n          </div>\n          <Button\n            onClick={onClose}\n            variant=\"outline\"\n            className=\"border-slate-600 text-slate-300 hover:text-white\"\n          >\n            ← Back to Quizzes\n          </Button>\n        </div>\n\n        {editingQuestion ? (\n          <QuestionForm\n            selectedQuizId={selectedQuizId}\n            editingQuestion={editingQuestion}\n            onQuestionSaved={handleQuestionSaved}\n            onCancel={handleCancelEdit}\n          />\n        ) : (\n          <QuestionForm\n            selectedQuizId={selectedQuizId}\n            onQuestionSaved={handleQuestionSaved}\n            onCancel={handleCancelEdit}\n          />\n        )}\n\n        <AiQuestionGenerator\n          selectedQuizId={selectedQuizId}\n          selectedQuizName={selectedQuizName}\n          studyDocumentId={studyDocumentId}\n          onGenerationComplete={handleGenerationComplete}\n        />\n\n        <QuestionsList\n          questions={questions}\n          loading={loading}\n          onRefreshQuestions={fetchQuestions}\n          selectedQuizId={selectedQuizId}\n        />\n      </div>\n    </div>\n  );\n};\n", "modifiedCode": "import React, { useEffect, useState, useRef, useCallback } from \"react\";\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables } from \"../../types/supabase\";\nimport { QuestionForm } from \"./QuestionForm\";\nimport { AiQuestionGenerator } from \"./AiQuestionGenerator\";\nimport { QuestionsList, QuestionsListProps } from \"./QuestionsList\";\nimport { Button } from \"@/components/ui/button\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\ninterface QuizQuestionManagerProps {\n  selectedQuizId: string;\n  selectedQuizName: string;\n  studyDocumentId?: string;\n  onClose: () => void;\n}\n\nexport const QuizQuestionManager: React.FC<QuizQuestionManagerProps> = ({\n  selectedQuizId,\n  selectedQuizName,\n  studyDocumentId,\n  onClose,\n}) => {\n  const { user, signOut } = useAuth();\n  const [questions, setQuestions] = useState<QuizQuestion[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(\n    null\n  );\n\n  // Refs to manage polling and prevent memory leaks\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const mountedRef = useRef(true);\n  const retryCountRef = useRef(0);\n  const maxRetries = 3;\n\n  // Clear polling interval\n  const clearPolling = useCallback(() => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n\n  // Handle authentication errors\n  const handleAuthError = useCallback(async () => {\n    console.warn('Authentication failed in QuizQuestionManager, signing out...');\n    clearPolling();\n    try {\n      await signOut();\n    } catch (error) {\n      console.error('Error during signOut:', error);\n    }\n  }, [signOut, clearPolling]);\n\n  const fetchQuestions = useCallback(async (isRetry = false) => {\n    if (!user || !selectedQuizId || !mountedRef.current) return;\n\n    // Don't show loading on retries to avoid UI flicker\n    if (!isRetry) {\n      setLoading(true);\n    }\n\n    try {\n      const token = localStorage.getItem('auth_token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const response = await fetch(`/api/quizzes/${selectedQuizId}/questions`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        if (response.status === 401) {\n          // Authentication failed - handle it properly\n          await handleAuthError();\n          return;\n        }\n\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch questions`);\n      }\n\n      const data = await response.json();\n\n      if (mountedRef.current) {\n        setQuestions(data || []);\n        setError(null);\n        retryCountRef.current = 0; // Reset retry count on success\n      }\n    } catch (err: any) {\n      console.error('Error fetching questions:', err);\n\n      if (mountedRef.current) {\n        // Only set error if we're still mounted and it's not an auth error\n        if (!err.message?.includes('authentication') && !err.message?.includes('token')) {\n          setError(err.message || \"Failed to fetch questions.\");\n\n          // Implement exponential backoff for retries\n          if (retryCountRef.current < maxRetries) {\n            retryCountRef.current++;\n            const delay = Math.pow(2, retryCountRef.current) * 1000; // 2s, 4s, 8s\n            console.log(`Retrying in ${delay}ms (attempt ${retryCountRef.current}/${maxRetries})`);\n\n            setTimeout(() => {\n              if (mountedRef.current) {\n                fetchQuestions(true);\n              }\n            }, delay);\n          }\n        }\n      }\n    } finally {\n      if (mountedRef.current && !isRetry) {\n        setLoading(false);\n      }\n    }\n  }, [user, selectedQuizId, handleAuthError]);\n\n  useEffect(() => {\n    fetchQuestions();\n\n    // Poll for updates every 5 seconds instead of realtime\n    const interval = setInterval(() => {\n      if (user && selectedQuizId) {\n        fetchQuestions();\n      }\n    }, 5000);\n\n    return () => {\n      clearInterval(interval);\n    };\n  }, [user, selectedQuizId]);\n\n  const handleQuestionSaved = () => {\n    setEditingQuestion(null);\n    fetchQuestions();\n  };\n\n  const handleCancelEdit = () => {\n    setEditingQuestion(null);\n  };\n\n  const handleGenerationComplete = () => {\n        fetchQuestions();\n  };\n\n  if (loading)\n    return (\n      <p className=\"text-slate-300\">\n        Loading questions for \"{selectedQuizName}\"...\n      </p>\n    );\n  if (error) return <p className=\"text-red-400\">Error: {error}</p>;\n\n  return (\n    <div className=\"mt-5 space-y-6\">\n      <div className=\"bg-slate-800 shadow-lg rounded-lg border border-slate-700 p-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <div>\n            <h4 className=\"text-2xl font-bold text-slate-200 mb-2\">\n              Manage Questions\n            </h4>\n            <p className=\"text-slate-400 text-sm\">\n              Quiz:{\" \"}\n              <span className=\"text-purple-400 font-medium\">\n                {selectedQuizName}\n              </span>\n            </p>\n          </div>\n          <Button\n            onClick={onClose}\n            variant=\"outline\"\n            className=\"border-slate-600 text-slate-300 hover:text-white\"\n          >\n            ← Back to Quizzes\n          </Button>\n        </div>\n\n        {editingQuestion ? (\n          <QuestionForm\n            selectedQuizId={selectedQuizId}\n            editingQuestion={editingQuestion}\n            onQuestionSaved={handleQuestionSaved}\n            onCancel={handleCancelEdit}\n          />\n        ) : (\n          <QuestionForm\n            selectedQuizId={selectedQuizId}\n            onQuestionSaved={handleQuestionSaved}\n            onCancel={handleCancelEdit}\n          />\n        )}\n\n        <AiQuestionGenerator\n          selectedQuizId={selectedQuizId}\n          selectedQuizName={selectedQuizName}\n          studyDocumentId={studyDocumentId}\n          onGenerationComplete={handleGenerationComplete}\n        />\n\n        <QuestionsList\n          questions={questions}\n          loading={loading}\n          onRefreshQuestions={fetchQuestions}\n          selectedQuizId={selectedQuizId}\n        />\n      </div>\n    </div>\n  );\n};\n"}