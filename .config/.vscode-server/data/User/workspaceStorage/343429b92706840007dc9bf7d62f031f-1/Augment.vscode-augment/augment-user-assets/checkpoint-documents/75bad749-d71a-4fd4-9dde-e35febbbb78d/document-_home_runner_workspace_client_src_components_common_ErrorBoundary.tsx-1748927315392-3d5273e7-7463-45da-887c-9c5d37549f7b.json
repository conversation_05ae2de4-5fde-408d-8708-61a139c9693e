{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/common/ErrorBoundary.tsx"}, "modifiedCode": "import React, { Component, ErrorInfo, ReactNode } from 'react';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n}\n\nexport class ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      // You can render any custom fallback UI\n      return this.props.fallback || (\n        <div className=\"flex flex-col items-center justify-center p-8 text-center\">\n          <h2 className=\"text-xl font-semibold text-red-600 mb-2\">\n            Something went wrong\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            An unexpected error occurred. Please refresh the page to try again.\n          </p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n          >\n            Refresh Page\n          </button>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n"}