{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardsList.tsx"}, "originalCode": "import React, { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Edit, Trash2, RotateCcw } from \"lucide-react\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { Tables } from \"@/types/supabase\";\nimport { useToast } from \"@/hooks/use-toast\";\n\ntype Flashcard = Tables<\"flashcards\">;\n\ninterface FlashcardsListProps {\n  flashcards: Flashcard[];\n  loading: boolean;\n  onRefreshFlashcards: () => void;\n  onEditFlashcard: (flashcard: Flashcard) => void;\n  selectedDeckId: string;\n}\n\nconst FlashcardsList: React.FC<FlashcardsListProps> = ({\n  flashcards,\n  loading,\n  onRefreshFlashcards,\n  onEditFlashcard,\n  selectedDeckId,\n}) => {\n  const { user } = useAuth();\n  const [deletingId, setDeletingId] = useState<string | null>(null);\n  const { toast } = useToast();\n\n  const handleDelete = async (flashcard: Flashcard) => {\n    if (!confirm(`Are you sure you want to delete this flashcard?`)) {\n      return;\n    }\n\n    if (!user) {\n      toast({\n        title: \"Error\",\n        description: \"You must be logged in to delete flashcards.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    setDeletingId(flashcard.id);\n    try {\n      // Delete flashcard via backend API\n      const token = localStorage.getItem('auth_token');\n      const response = await fetch(`/api/flashcards/${flashcard.id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to delete flashcard');\n      }\n\n      onRefreshFlashcards();\n      toast({\n        title: \"Success\",\n        description: \"Flashcard deleted successfully.\",\n      });\n    } catch (error: any) {\n      console.error(\"Error deleting flashcard:\", error);\n      toast({\n        title: \"Error\",\n        description: error.message || \"Failed to delete flashcard.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setDeletingId(null);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getSRSStatus = (flashcard: Flashcard) => {\n    // Since SRS fields don't exist in current flashcard schema, treat all as \"New\"\n    // This can be updated when SRS functionality is properly implemented for flashcards\n    return { label: \"New\", color: \"bg-blue-500\" };\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-4\">\n        {[...Array(3)].map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-24 bg-slate-700 rounded-lg\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (flashcards.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"text-slate-400 mb-4\">\n          <RotateCcw className=\"h-12 w-12 mx-auto mb-2 opacity-50\" />\n          <p>No flashcards found in this deck.</p>\n          <p className=\"text-sm\">Add some flashcards to get started!</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex justify-between items-center\">\n        <p className=\"text-sm text-slate-400\">\n          {flashcards.length} flashcard{flashcards.length !== 1 ? \"s\" : \"\"}\n        </p>\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={onRefreshFlashcards}\n          className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n        >\n          <RotateCcw className=\"h-4 w-4 mr-2\" />\n          Refresh\n        </Button>\n      </div>\n\n      <div className=\"space-y-3\">\n        {flashcards.map((flashcard, index) => {\n          const srsStatus = getSRSStatus(flashcard);\n          \n          return (\n            <Card\n              key={flashcard.id}\n              className=\"bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-colors\"\n            >\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                      <span className=\"text-slate-400 font-mono text-sm bg-slate-800 px-2 py-1 rounded\">\n                        #{index + 1}\n                      </span>\n                      <span\n                        className={`${srsStatus.color} text-white text-xs px-2 py-1 rounded-full`}\n                      >\n                        {srsStatus.label}\n                      </span>\n                      <span className=\"text-xs text-slate-500\">\n                        Created: {flashcard.created_at ? formatDate(flashcard.created_at) : 'Unknown'}\n                      </span>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <div>\n                        <p className=\"text-xs text-slate-400 mb-1\">Question:</p>\n                        <p className=\"text-slate-200 text-sm line-clamp-2\">\n                          {flashcard.front_text}\n                        </p>\n                      </div>\n\n                      <div>\n                        <p className=\"text-xs text-slate-400 mb-1\">Answer:</p>\n                        <p className=\"text-slate-300 text-sm line-clamp-2\">\n                          {flashcard.back_text}\n                        </p>\n                      </div>\n                    </div>\n\n                    {/* SRS statistics not available in current schema */}\n                    <div className=\"mt-2 text-xs text-slate-500\">\n                      SRS tracking not yet implemented for flashcards\n                    </div>\n                  </div>\n\n                  <div className=\"flex space-x-2 ml-4\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => onEditFlashcard(flashcard)}\n                      className=\"border-slate-600 text-slate-300 hover:bg-slate-600\"\n                    >\n                      <Edit className=\"h-4 w-4\" />\n                    </Button>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => handleDelete(flashcard)}\n                      disabled={deletingId === flashcard.id}\n                      className=\"border-red-600 text-red-400 hover:bg-red-900/20\"\n                    >\n                      <Trash2 className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default FlashcardsList;\n", "modifiedCode": "import React, { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Edit, Trash2, RotateCcw } from \"lucide-react\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { Tables } from \"@/types/supabase\";\nimport { useToast } from \"@/hooks/use-toast\";\n\ntype Flashcard = Tables<\"flashcards\">;\n\ninterface FlashcardsListProps {\n  flashcards: Flashcard[];\n  loading: boolean;\n  onRefreshFlashcards: () => void;\n  onEditFlashcard: (flashcard: Flashcard) => void;\n  selectedDeckId: string;\n}\n\nconst FlashcardsList: React.FC<FlashcardsListProps> = ({\n  flashcards,\n  loading,\n  onRefreshFlashcards,\n  onEditFlashcard,\n  selectedDeckId,\n}) => {\n  const { user } = useAuth();\n  const [deletingId, setDeletingId] = useState<string | null>(null);\n  const { toast } = useToast();\n\n  const handleDelete = async (flashcard: Flashcard) => {\n    if (!confirm(`Are you sure you want to delete this flashcard?`)) {\n      return;\n    }\n\n    if (!user) {\n      toast({\n        title: \"Error\",\n        description: \"You must be logged in to delete flashcards.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    setDeletingId(flashcard.id);\n    try {\n      // Delete flashcard via backend API\n      const token = localStorage.getItem('auth_token');\n      const response = await fetch(`/api/flashcards/${flashcard.id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to delete flashcard');\n      }\n\n      onRefreshFlashcards();\n      toast({\n        title: \"Success\",\n        description: \"Flashcard deleted successfully.\",\n      });\n    } catch (error: any) {\n      console.error(\"Error deleting flashcard:\", error);\n      toast({\n        title: \"Error\",\n        description: error.message || \"Failed to delete flashcard.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setDeletingId(null);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getSRSStatus = (flashcard: Flashcard) => {\n    // Since SRS fields don't exist in current flashcard schema, treat all as \"New\"\n    // This can be updated when SRS functionality is properly implemented for flashcards\n    return { label: \"New\", color: \"bg-blue-500\" };\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-4\">\n        {[...Array(3)].map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-24 bg-slate-700 rounded-lg\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (flashcards.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"text-slate-400 mb-4\">\n          <RotateCcw className=\"h-12 w-12 mx-auto mb-2 opacity-50\" />\n          <p>No flashcards found in this deck.</p>\n          <p className=\"text-sm\">Add some flashcards to get started!</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex justify-between items-center\">\n        <p className=\"text-sm text-slate-400\">\n          {flashcards.length} flashcard{flashcards.length !== 1 ? \"s\" : \"\"}\n        </p>\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={onRefreshFlashcards}\n          className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n        >\n          <RotateCcw className=\"h-4 w-4 mr-2\" />\n          Refresh\n        </Button>\n      </div>\n\n      <div className=\"space-y-3\">\n        {flashcards.map((flashcard, index) => {\n          const srsStatus = getSRSStatus(flashcard);\n          \n          return (\n            <Card\n              key={flashcard.id}\n              className=\"bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-colors\"\n            >\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                      <span className=\"text-slate-400 font-mono text-sm bg-slate-800 px-2 py-1 rounded\">\n                        #{index + 1}\n                      </span>\n                      <span\n                        className={`${srsStatus.color} text-white text-xs px-2 py-1 rounded-full`}\n                      >\n                        {srsStatus.label}\n                      </span>\n                      <span className=\"text-xs text-slate-500\">\n                        Created: {flashcard.created_at ? formatDate(flashcard.created_at) : 'Unknown'}\n                      </span>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <div>\n                        <p className=\"text-xs text-slate-400 mb-1\">Question:</p>\n                        <p className=\"text-slate-200 text-sm line-clamp-2\">\n                          {flashcard.front_text}\n                        </p>\n                      </div>\n\n                      <div>\n                        <p className=\"text-xs text-slate-400 mb-1\">Answer:</p>\n                        <p className=\"text-slate-300 text-sm line-clamp-2\">\n                          {flashcard.back_text}\n                        </p>\n                      </div>\n                    </div>\n\n                    {/* SRS statistics not available in current schema */}\n                    <div className=\"mt-2 text-xs text-slate-500\">\n                      SRS tracking not yet implemented for flashcards\n                    </div>\n                  </div>\n\n                  <div className=\"flex space-x-2 ml-4\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => onEditFlashcard(flashcard)}\n                      className=\"border-slate-600 text-slate-300 hover:bg-slate-600\"\n                    >\n                      <Edit className=\"h-4 w-4\" />\n                    </Button>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => handleDelete(flashcard)}\n                      disabled={deletingId === flashcard.id}\n                      className=\"border-red-600 text-red-400 hover:bg-red-900/20\"\n                    >\n                      <Trash2 className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default FlashcardsList;\n"}