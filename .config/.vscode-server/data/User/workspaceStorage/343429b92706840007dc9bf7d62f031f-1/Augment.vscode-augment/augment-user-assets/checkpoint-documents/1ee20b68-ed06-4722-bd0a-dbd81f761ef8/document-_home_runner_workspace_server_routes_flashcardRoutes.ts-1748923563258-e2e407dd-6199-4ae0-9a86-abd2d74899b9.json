{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/flashcardRoutes.ts"}, "originalCode": "import { Hono } from \"hono\";\nimport type { Context } from \"hono\";\nimport type { AppVariables } from \"../middleware/authMiddleware\";\n\nconst flashcardRoutes = new Hono<{ Variables: AppVariables }>();\n\n// Route to update an individual flashcard\nflashcardRoutes.put(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    const body = await c.req.json();\n    const { front_text, back_text } = body;\n\n    if (!front_text || !back_text) {\n      return c.json({ error: \"Both front_text and back_text are required\" }, 400);\n    }\n\n    // First verify the flashcard exists and user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"id, user_id, set_id\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error checking flashcard ownership:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    // Update the flashcard\n    const { data: updatedFlashcard, error: updateError } = await supabase\n      .from(\"flashcards\")\n      .update({\n        front_text: front_text.trim(),\n        back_text: back_text.trim(),\n        updated_at: new Date().toISOString(),\n      })\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .select()\n      .single();\n\n    if (updateError) {\n      console.error(\"Error updating flashcard:\", updateError);\n      return c.json(\n        { error: \"Failed to update flashcard\", details: updateError.message },\n        500\n      );\n    }\n\n    return c.json({\n      message: \"Flashcard updated successfully\",\n      flashcard: updatedFlashcard,\n    }, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard update:\", error);\n    return c.json({ error: error.message || \"Failed to update flashcard\" }, 500);\n  }\n});\n\n// Route to delete an individual flashcard\nflashcardRoutes.delete(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    // First verify the flashcard exists and user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"id, user_id, set_id\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error checking flashcard ownership:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    // Delete the flashcard\n    const { error: deleteError } = await supabase\n      .from(\"flashcards\")\n      .delete()\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id);\n\n    if (deleteError) {\n      console.error(\"Error deleting flashcard:\", deleteError);\n      return c.json(\n        { error: \"Failed to delete flashcard\", details: deleteError.message },\n        500\n      );\n    }\n\n    return c.json({\n      message: \"Flashcard deleted successfully\",\n    }, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard deletion:\", error);\n    return c.json({ error: error.message || \"Failed to delete flashcard\" }, 500);\n  }\n});\n\n// Route to get an individual flashcard\nflashcardRoutes.get(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    // Get the flashcard if user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"*\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error fetching flashcard:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to fetch flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    return c.json(flashcard, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard fetch:\", error);\n    return c.json({ error: error.message || \"Failed to fetch flashcard\" }, 500);\n  }\n});\n\nexport default flashcardRoutes;\n", "modifiedCode": "import { Hono } from \"hono\";\nimport type { Context } from \"hono\";\nimport type { AppVariables } from \"../middleware/authMiddleware\";\nimport { authMiddleware } from \"../middleware/authMiddleware\";\n\nconst flashcardRoutes = new Hono<{ Variables: AppVariables }>();\n\n// Apply authentication middleware to all routes\nflashcardRoutes.use(\"*\", authMiddleware);\n\n// Route to update an individual flashcard\nflashcardRoutes.put(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    const body = await c.req.json();\n    const { front_text, back_text } = body;\n\n    if (!front_text || !back_text) {\n      return c.json({ error: \"Both front_text and back_text are required\" }, 400);\n    }\n\n    // First verify the flashcard exists and user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"id, user_id, set_id\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error checking flashcard ownership:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    // Update the flashcard\n    const { data: updatedFlashcard, error: updateError } = await supabase\n      .from(\"flashcards\")\n      .update({\n        front_text: front_text.trim(),\n        back_text: back_text.trim(),\n        updated_at: new Date().toISOString(),\n      })\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .select()\n      .single();\n\n    if (updateError) {\n      console.error(\"Error updating flashcard:\", updateError);\n      return c.json(\n        { error: \"Failed to update flashcard\", details: updateError.message },\n        500\n      );\n    }\n\n    return c.json({\n      message: \"Flashcard updated successfully\",\n      flashcard: updatedFlashcard,\n    }, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard update:\", error);\n    return c.json({ error: error.message || \"Failed to update flashcard\" }, 500);\n  }\n});\n\n// Route to delete an individual flashcard\nflashcardRoutes.delete(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    // First verify the flashcard exists and user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"id, user_id, set_id\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error checking flashcard ownership:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    // Delete the flashcard\n    const { error: deleteError } = await supabase\n      .from(\"flashcards\")\n      .delete()\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id);\n\n    if (deleteError) {\n      console.error(\"Error deleting flashcard:\", deleteError);\n      return c.json(\n        { error: \"Failed to delete flashcard\", details: deleteError.message },\n        500\n      );\n    }\n\n    return c.json({\n      message: \"Flashcard deleted successfully\",\n    }, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard deletion:\", error);\n    return c.json({ error: error.message || \"Failed to delete flashcard\" }, 500);\n  }\n});\n\n// Route to get an individual flashcard\nflashcardRoutes.get(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    // Get the flashcard if user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"*\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error fetching flashcard:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to fetch flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    return c.json(flashcard, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard fetch:\", error);\n    return c.json({ error: error.message || \"Failed to fetch flashcard\" }, 500);\n  }\n});\n\nexport default flashcardRoutes;\n"}