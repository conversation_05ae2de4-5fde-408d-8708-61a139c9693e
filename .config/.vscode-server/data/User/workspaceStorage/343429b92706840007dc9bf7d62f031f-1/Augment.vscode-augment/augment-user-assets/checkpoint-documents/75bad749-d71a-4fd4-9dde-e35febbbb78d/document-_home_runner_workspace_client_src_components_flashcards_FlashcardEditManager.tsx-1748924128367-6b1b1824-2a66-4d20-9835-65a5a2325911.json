{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardEditManager.tsx"}, "originalCode": "import React, { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { ArrowLeft, Plus } from \"lucide-react\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { Tables } from \"@/types/supabase\";\nimport FlashcardForm from \"./FlashcardForm\";\nimport FlashcardsList from \"./FlashcardsList\";\nimport AiFlashcardGenerator from \"./AiFlashcardGenerator\";\n\ntype Flashcard = Tables<\"flashcards\">;\n\ninterface FlashcardEditManagerProps {\n  selectedDeckId: string;\n  selectedDeckName: string;\n  onClose: () => void;\n}\n\nconst FlashcardEditManager: React.FC<FlashcardEditManagerProps> = ({\n  selectedDeckId,\n  selectedDeckName,\n  onClose,\n}) => {\n  const { user } = useAuth();\n  const [editingFlashcard, setEditingFlashcard] = useState<Flashcard | null>(null);\n  const [showForm, setShowForm] = useState(false);\n  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchFlashcards = async () => {\n    if (!user || !selectedDeckId) return;\n\n    console.log(\"🔄 fetchFlashcards called for deck:\", selectedDeckId);\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Fetch flashcard set from backend API\n      const token = localStorage.getItem('auth_token');\n      const response = await fetch(`/api/flashcard-sets/${selectedDeckId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch flashcard set');\n      }\n\n      const data = await response.json();\n      console.log(\"📋 Fetched flashcards data:\", {\n        flashcardCount: data.flashcards?.length || 0,\n        flashcards: data.flashcards?.map((f: any) => ({\n          id: f.id,\n          front_text: f.front_text?.substring(0, 50) + \"...\",\n          back_text: f.back_text?.substring(0, 50) + \"...\",\n          updated_at: f.updated_at\n        }))\n      });\n      setFlashcards(data.flashcards || []);\n    } catch (err: any) {\n      console.error(\"❌ Error fetching flashcards:\", err);\n      setError(\"Failed to fetch flashcards\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchFlashcards();\n    // Real-time subscriptions removed - using backend API only\n  }, [user, selectedDeckId]);\n\n  const handleFlashcardSaved = () => {\n    setEditingFlashcard(null);\n    setShowForm(false);\n    fetchFlashcards();\n  };\n\n  const handleCancelEdit = () => {\n    setEditingFlashcard(null);\n    setShowForm(false);\n  };\n\n  const handleEditFlashcard = (flashcard: Flashcard) => {\n    setEditingFlashcard(flashcard);\n    setShowForm(true);\n  };\n\n  const handleAddNew = () => {\n    setEditingFlashcard(null);\n    setShowForm(true);\n  };\n\n  const handleGenerationComplete = () => {\n    fetchFlashcards();\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Button\n            variant=\"ghost\"\n            onClick={onClose}\n            className=\"text-slate-400 hover:text-slate-300\"\n          >\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back\n          </Button>\n          <div>\n            <h1 className=\"text-2xl font-bold text-slate-100\">\n              Edit Flashcards\n            </h1>\n            <p className=\"text-slate-400\">{selectedDeckName}</p>\n          </div>\n        </div>\n        <Button\n          onClick={handleAddNew}\n          className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Flashcard\n        </Button>\n      </div>\n\n      {/* Manual Form */}\n      {showForm && (\n        <Card className=\"bg-slate-800 border-slate-700\">\n          <CardHeader>\n            <CardTitle className=\"text-slate-100\">\n              {editingFlashcard ? \"Edit Flashcard\" : \"Add New Flashcard\"}\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <FlashcardForm\n              selectedDeckId={selectedDeckId}\n              editingFlashcard={editingFlashcard}\n              onFlashcardSaved={handleFlashcardSaved}\n              onCancel={handleCancelEdit}\n            />\n          </CardContent>\n        </Card>\n      )}\n\n      {/* AI Generator */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-slate-100\">AI Flashcard Generator</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <AiFlashcardGenerator\n            selectedDeckId={selectedDeckId}\n            selectedDeckName={selectedDeckName}\n            onGenerationComplete={handleGenerationComplete}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Flashcards List */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-slate-100\">\n            Existing Flashcards ({flashcards.length})\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <FlashcardsList\n            flashcards={flashcards}\n            loading={isLoading}\n            onRefreshFlashcards={fetchFlashcards}\n            onEditFlashcard={handleEditFlashcard}\n            selectedDeckId={selectedDeckId}\n          />\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n\nexport default FlashcardEditManager;\n", "modifiedCode": "import React, { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { ArrowLeft, Plus } from \"lucide-react\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { Tables } from \"@/types/supabase\";\nimport FlashcardForm from \"./FlashcardForm\";\nimport FlashcardsList from \"./FlashcardsList\";\nimport AiFlashcardGenerator from \"./AiFlashcardGenerator\";\n\ntype Flashcard = Tables<\"flashcards\">;\n\ninterface FlashcardEditManagerProps {\n  selectedDeckId: string;\n  selectedDeckName: string;\n  onClose: () => void;\n}\n\nconst FlashcardEditManager: React.FC<FlashcardEditManagerProps> = ({\n  selectedDeckId,\n  selectedDeckName,\n  onClose,\n}) => {\n  const { user } = useAuth();\n  const [editingFlashcard, setEditingFlashcard] = useState<Flashcard | null>(null);\n  const [showForm, setShowForm] = useState(false);\n  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchFlashcards = async () => {\n    if (!user || !selectedDeckId) return;\n\n    console.log(\"🔄 fetchFlashcards called for deck:\", selectedDeckId);\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Fetch flashcard set from backend API\n      const token = localStorage.getItem('auth_token');\n      const response = await fetch(`/api/flashcard-sets/${selectedDeckId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch flashcard set');\n      }\n\n      const data = await response.json();\n      console.log(\"📋 Fetched flashcards data:\", {\n        flashcardCount: data.flashcards?.length || 0,\n        flashcards: data.flashcards?.map((f: any) => ({\n          id: f.id,\n          front_text: f.front_text?.substring(0, 50) + \"...\",\n          back_text: f.back_text?.substring(0, 50) + \"...\",\n          updated_at: f.updated_at\n        }))\n      });\n      setFlashcards(data.flashcards || []);\n    } catch (err: any) {\n      console.error(\"❌ Error fetching flashcards:\", err);\n      setError(\"Failed to fetch flashcards\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchFlashcards();\n    // Real-time subscriptions removed - using backend API only\n  }, [user, selectedDeckId]);\n\n  const handleFlashcardSaved = () => {\n    setEditingFlashcard(null);\n    setShowForm(false);\n    fetchFlashcards();\n  };\n\n  const handleCancelEdit = () => {\n    setEditingFlashcard(null);\n    setShowForm(false);\n  };\n\n  const handleEditFlashcard = (flashcard: Flashcard) => {\n    setEditingFlashcard(flashcard);\n    setShowForm(true);\n  };\n\n  const handleAddNew = () => {\n    setEditingFlashcard(null);\n    setShowForm(true);\n  };\n\n  const handleGenerationComplete = () => {\n    fetchFlashcards();\n  };\n\n  const handleUpdateFlashcard = (updatedFlashcard: Flashcard) => {\n    console.log(\"🔄 Updating flashcard in local state:\", updatedFlashcard.id);\n    setFlashcards(prevFlashcards =>\n      prevFlashcards.map(flashcard =>\n        flashcard.id === updatedFlashcard.id ? updatedFlashcard : flashcard\n      )\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Button\n            variant=\"ghost\"\n            onClick={onClose}\n            className=\"text-slate-400 hover:text-slate-300\"\n          >\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back\n          </Button>\n          <div>\n            <h1 className=\"text-2xl font-bold text-slate-100\">\n              Edit Flashcards\n            </h1>\n            <p className=\"text-slate-400\">{selectedDeckName}</p>\n          </div>\n        </div>\n        <Button\n          onClick={handleAddNew}\n          className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Flashcard\n        </Button>\n      </div>\n\n      {/* Manual Form */}\n      {showForm && (\n        <Card className=\"bg-slate-800 border-slate-700\">\n          <CardHeader>\n            <CardTitle className=\"text-slate-100\">\n              {editingFlashcard ? \"Edit Flashcard\" : \"Add New Flashcard\"}\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <FlashcardForm\n              selectedDeckId={selectedDeckId}\n              editingFlashcard={editingFlashcard}\n              onFlashcardSaved={handleFlashcardSaved}\n              onCancel={handleCancelEdit}\n            />\n          </CardContent>\n        </Card>\n      )}\n\n      {/* AI Generator */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-slate-100\">AI Flashcard Generator</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <AiFlashcardGenerator\n            selectedDeckId={selectedDeckId}\n            selectedDeckName={selectedDeckName}\n            onGenerationComplete={handleGenerationComplete}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Flashcards List */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-slate-100\">\n            Existing Flashcards ({flashcards.length})\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <FlashcardsList\n            flashcards={flashcards}\n            loading={isLoading}\n            onRefreshFlashcards={fetchFlashcards}\n            onEditFlashcard={handleEditFlashcard}\n            selectedDeckId={selectedDeckId}\n          />\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n\nexport default FlashcardEditManager;\n"}