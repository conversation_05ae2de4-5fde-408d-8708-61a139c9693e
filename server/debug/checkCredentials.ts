/**
 * Debug script to check user credentials in the database
 * Run this to verify that credentials are properly stored and accessible
 */

import { createClient } from '@supabase/supabase-js';
import { supabaseConfig } from '../config';

const supabase = createClient(supabaseConfig.url, supabaseConfig.serviceRoleKey);

async function checkCredentialsTable() {
  console.log('🔍 Checking user_ai_credentials table...');
  
  try {
    // First, check if the table exists and get its structure
    const { data: tableInfo, error: tableError } = await supabase
      .from('user_ai_credentials')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error('❌ Error accessing user_ai_credentials table:', tableError);
      return;
    }

    console.log('✅ Table exists and is accessible');

    // Get all records to see what's stored
    const { data: allCredentials, error: allError } = await supabase
      .from('user_ai_credentials')
      .select('user_id, provider, base_url, extraction_model, generation_model, created_at, updated_at');

    if (allError) {
      console.error('❌ Error fetching all credentials:', allError);
      return;
    }

    console.log(`📊 Found ${allCredentials?.length || 0} credential records:`);
    allCredentials?.forEach((cred, index) => {
      console.log(`  ${index + 1}. User: ${cred.user_id}, Provider: ${cred.provider}, Base URL: ${cred.base_url}`);
      console.log(`     Models: extraction=${cred.extraction_model}, generation=${cred.generation_model}`);
      console.log(`     Created: ${cred.created_at}, Updated: ${cred.updated_at}`);
    });

    // Check for OpenRouter specifically
    const { data: openRouterCreds, error: openRouterError } = await supabase
      .from('user_ai_credentials')
      .select('*')
      .eq('provider', 'OpenRouter');

    if (openRouterError) {
      console.error('❌ Error fetching OpenRouter credentials:', openRouterError);
      return;
    }

    console.log(`🎯 Found ${openRouterCreds?.length || 0} OpenRouter credential records`);

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

async function checkSpecificUser(userId: string) {
  console.log(`🔍 Checking credentials for specific user: ${userId}`);
  
  try {
    const { data, error } = await supabase
      .from('user_ai_credentials')
      .select('*')
      .eq('user_id', userId)
      .eq('provider', 'OpenRouter')
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        console.log(`📭 No OpenRouter credentials found for user ${userId}`);
      } else {
        console.error('❌ Error fetching user credentials:', error);
      }
      return;
    }

    console.log(`✅ Found OpenRouter credentials for user ${userId}:`, {
      provider: data.provider,
      baseUrl: data.base_url,
      extractionModel: data.extraction_model,
      generationModel: data.generation_model,
      hasEncryptedKey: !!data.encrypted_api_key,
      hasIV: !!data.encryption_iv,
      hasTag: !!data.encryption_tag,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    });

  } catch (error) {
    console.error('❌ Unexpected error checking user credentials:', error);
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting credentials diagnostic...\n');
  
  await checkCredentialsTable();
  
  console.log('\n' + '='.repeat(50));
  console.log('To check a specific user, call:');
  console.log('checkSpecificUser("your-user-id-here")');
  console.log('='.repeat(50));
}

// Export functions for manual testing
export { checkCredentialsTable, checkSpecificUser };

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
