import React from "react";
import { useQuery } from "@tanstack/react-query";
import { getDashboardStatsAPI } from "@/lib/api";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { FileTex<PERSON>, BookOpen, FileQuestion, Trophy } from "lucide-react";

interface StatCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: React.ReactElement;
  isLoading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  description,
  icon,
  isLoading,
}) => {
  const cardClasses =
    "bg-slate-800 border-slate-700 text-purple-400 shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 group";
  const iconElementClasses = "h-5 w-5 text-purple-400";

  if (isLoading) {
    return (
      <Card className={cardClasses}>
        <CardContent className="p-5 flex flex-col space-y-2">
          <div className="flex items-center justify-between w-full">
            <Skeleton className="h-5 w-3/5 bg-slate-700" />
            <Skeleton className="h-9 w-9 rounded-lg bg-slate-700" />
          </div>
          <Skeleton className="h-8 w-2/5 bg-slate-700" />
          <Skeleton className="h-4 w-4/5 bg-slate-700" />
        </CardContent>
      </Card>
    );
  }

  const titleClasses = "text-base font-medium text-slate-300";
  const valueClasses =
    "text-2xl font-bold text-purple-400 group-hover:text-purple-300 transition-colors";
  const descriptionClasses =
    "text-xs text-slate-400 group-hover:text-slate-300 transition-colors";
  const iconContainerClasses =
    "p-2 bg-purple-500/20 group-hover:bg-purple-500/30 transition-colors rounded-lg flex items-center justify-center";

  return (
    <Card className={`${cardClasses} group`}>
      <CardContent className="p-5 flex flex-col space-y-2">
        <div className="flex items-start justify-between w-full">
          <h3 className={titleClasses}>{title}</h3>
          <div className={iconContainerClasses}>
            {React.cloneElement(icon, { className: iconElementClasses })}
          </div>
        </div>
        <p className={valueClasses}>{value}</p>
        <p className={descriptionClasses}>{description}</p>
      </CardContent>
    </Card>
  );
};

const DashboardOverview: React.FC = () => {
  const { data: stats, isLoading, error } = useQuery({
    queryKey: ["dashboard-stats"],
    queryFn: getDashboardStatsAPI,
    retry: 3,
    retryDelay: 1000,
  });

  const overviewItems = [
    {
      title: "Documents",
      getValue: (s: any) => s?.totalDocuments || 0,
      description: "Total uploaded",
      icon: <FileText />,
    },
    {
      title: "Total Flashcards",
      getValue: (s: any) => s?.totalFlashcards || 0,
      description: "All flashcards created",
      icon: <BookOpen />,
    },
    {
      title: "Total Quizzes",
      getValue: (s: any) => s?.totalQuizQuestions || 0,
      description: "Questions across all quizzes",
      icon: <FileQuestion />,
    },
    {
      title: "Completions",
      getValue: (s: any) => s?.totalCompletions || 0,
      description: "Quizzes/sets completed",
      icon: <Trophy />,
    },
  ];

  // Show error state if there's an error
  if (error) {
    console.error("Dashboard stats error:", error);
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {overviewItems.map((item) => (
          <Card key={item.title} className="bg-slate-800 border-slate-700 text-purple-400">
            <CardContent className="p-5 flex flex-col space-y-2">
              <div className="flex items-start justify-between w-full">
                <h3 className="text-base font-medium text-slate-300">{item.title}</h3>
                <div className="p-2 bg-red-500/20 rounded-lg flex items-center justify-center">
                  {React.cloneElement(item.icon, { className: "h-5 w-5 text-red-400" })}
                </div>
              </div>
              <p className="text-2xl font-bold text-red-400">Error</p>
              <p className="text-xs text-slate-400">Failed to load data</p>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {[...Array(4)].map((_, i) => (
          <StatCard
            key={`skeleton-${i}`}
            title=""
            value=""
            description=""
            icon={<div />}
            isLoading={true}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {overviewItems.map((item) => (
        <StatCard
          key={item.title}
          title={item.title}
          value={item.getValue(stats)}
          description={item.description}
          icon={item.icon}
          isLoading={false}
        />
      ))}
    </div>
  );
};

export default DashboardOverview;
