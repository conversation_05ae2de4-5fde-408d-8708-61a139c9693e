#!/bin/bash

# Clean development startup script for Replit
echo "🧹 Cleaning up existing processes..."

# Kill any existing Node.js processes that might be using our ports
pkill -f "node.*server" 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true
pkill -f "tsx" 2>/dev/null || true

# Wait a moment for processes to clean up
sleep 2

echo "🚀 Starting development servers..."

# Start the development servers
npm run dev
